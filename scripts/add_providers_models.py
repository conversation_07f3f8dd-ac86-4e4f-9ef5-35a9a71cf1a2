import json
from datetime import datetime
from app.core.config import settings
from app.models.provider import Provider, Model
from app.db.session import SessionLocal

session = SessionLocal()

# Load models_providers.json
with open("scripts/models_providers.json", "r") as f:
    data = json.load(f)

COMMON_BASE_URL = "https://router.requesty.ai/v1"
providers_seen = {}

for item in data:
    provider_name = item["provider"]
    model_name = item["model"]
    max_tokens = item.get("max_output_tokens")

    # Handle pricing safely
    price_raw = item.get("output_tokens_price_per_million")
    price = float(price_raw) / 1_000_000 if price_raw else None

    # Default description fallback
    description = item.get("description") or f"{provider_name.capitalize()} provides high-performance models like {model_name} for AI tasks."

    # Create or reuse provider
    if provider_name not in providers_seen:
        provider = session.query(Provider).filter_by(provider=provider_name).first()
        if not provider:
            provider = Provider(
                provider=provider_name,
                description=f"{provider_name.capitalize()} is an integrated model provider using Requestly router.",
                base_url=COMMON_BASE_URL,
                is_active=True,
                is_default=False,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(provider)
            session.flush()
        providers_seen[provider_name] = provider
    else:
        provider = providers_seen[provider_name]

    # Avoid duplicate models
    existing_model = (
        session.query(Model)
        .filter_by(provider_id=provider.id, model=model_name)
        .first()
    )
    if existing_model:
        continue

    # Add model
    new_model = Model(
        provider_id=provider.id,
        model=model_name,
        model_id=model_name,
        description=description,
        price_per_tokens=price,
        max_tokens=max_tokens,
        temperature=0.7,
        provider_type="chat",
        is_active=True,
        is_default=False,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    session.add(new_model)

# Final commit
session.commit()
session.close()
print("✅ All providers and models from JSON have been added.")
