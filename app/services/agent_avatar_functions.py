import grpc
import structlog
from app.db.session import Session<PERSON>ocal
from app.models.agent import AgentAvatar
from app.grpc import agent_pb2, agent_pb2_grpc
from app.utils.helpers.agent_to_protobuf import _agent_avatar_to_protobuf

logger = structlog.get_logger()


class AgentAvatarFunctionsService(agent_pb2_grpc.AgentServiceServicer):
    """
    Service for managing agent avatars.

    This service handles CRUD operations for agent avatars, including
    validation, storage, and database operations.
    """

    def get_db(self):
        """Get a database session."""
        db = SessionLocal()
        try:
            return db
        except Exception as e:
            db.close()
            raise e

    def createAgentAvatar(
        self, request: agent_pb2.CreateAgentAvatarRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateAgentAvatarResponse:
        """
        Creates a new agent avatar.

        Args:
            request: The create avatar request
            context: The gRPC service context

        Returns:
            agent_pb2.CreateAgentAvatarResponse: Response containing the created avatar details
        """
        db = self.get_db()
        logger.info("create_agent_avatar_request", url=request.url)

        try:
            # Create new avatar
            new_avatar = AgentAvatar(
                url=request.url,
            )

            # Add to database
            db.add(new_avatar)
            db.commit()
            db.refresh(new_avatar)
            logger.info("create_agent_avatar_success", id=new_avatar.id)

            # Convert to protobuf and return
            avatar_proto = _agent_avatar_to_protobuf(new_avatar)
            print(f"[DEBUG] Created avatar: {avatar_proto}")
            return agent_pb2.CreateAgentAvatarResponse(
                success=True,
                message="Avatar created successfully",
                avatar=avatar_proto,
            )
        except Exception as e:
            print(f"[ERROR] {e}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to create avatar: {str(e)}")
            return agent_pb2.CreateAgentAvatarResponse(
                success=False,
                message=f"Failed to create avatar: {str(e)}",
                avatar=None,
            )
        finally:
            db.close()

    def getAgentAvatar(
        self, request: agent_pb2.GetAgentAvatarRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetAgentAvatarResponse:
        """
        Gets an agent avatar by ID.

        Args:
            request: The get avatar request
            context: The gRPC service context

        Returns:
            agent_pb2.GetAgentAvatarResponse: Response containing the avatar details
        """
        db = self.get_db()
        logger.info("get_agent_avatar_request", id=request.id)

        try:
            # Get avatar from database
            avatar = db.query(AgentAvatar).filter(AgentAvatar.id == request.id).first()

            if not avatar:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Avatar with ID {request.id} not found")
                return agent_pb2.GetAgentAvatarResponse(
                    success=False,
                    message=f"Avatar with ID {request.id} not found",
                    avatar=None,
                )

            # Convert to protobuf and return
            avatar_proto = _agent_avatar_to_protobuf(avatar)
            return agent_pb2.GetAgentAvatarResponse(
                success=True,
                message="Avatar retrieved successfully",
                avatar=avatar_proto,
            )
        except Exception as e:
            logger.error(f"get_agent_avatar_failed | {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get avatar: {str(e)}")
            return agent_pb2.GetAgentAvatarResponse(
                success=False,
                message=f"Failed to get avatar: {str(e)}",
                avatar=None,
            )
        finally:
            db.close()

    def listAgentAvatars(
        self, request: agent_pb2.ListAgentAvatarsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListAgentAvatarsResponse:
        """
        Lists all agent avatars with pagination.

        Args:
            request: The list avatars request
            context: The gRPC service context

        Returns:
            agent_pb2.ListAgentAvatarsResponse: Response containing the list of avatars
        """
        db = self.get_db()
        logger.info("list_agent_avatars_request", page=request.page, page_size=request.page_size)

        try:
            # Get total count
            total = db.query(AgentAvatar).count()

            # Calculate pagination
            page = max(1, request.page)
            page_size = max(1, min(100, request.page_size))
            offset = (page - 1) * page_size
            total_pages = (total + page_size - 1) // page_size

            # Get avatars from database
            avatars = db.query(AgentAvatar).offset(offset).limit(page_size).all()

            # Convert to protobuf and return
            avatar_protos = [_agent_avatar_to_protobuf(avatar) for avatar in avatars]
            return agent_pb2.ListAgentAvatarsResponse(
                success=True,
                message="Avatars retrieved successfully",
                avatars=avatar_protos,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            logger.error(f"list_agent_avatars_failed | {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list avatars: {str(e)}")
            return agent_pb2.ListAgentAvatarsResponse(
                success=False,
                message=f"Failed to list avatars: {str(e)}",
                avatars=[],
                total=0,
                page=request.page,
                total_pages=0,
            )
        finally:
            db.close()

    def deleteAgentAvatar(
        self, request: agent_pb2.DeleteAgentAvatarRequest, context: grpc.ServicerContext
    ) -> agent_pb2.DeleteAgentAvatarResponse:
        """
        Deletes an agent avatar.

        Args:
            request: The delete avatar request
            context: The gRPC service context

        Returns:
            agent_pb2.DeleteAgentAvatarResponse: Response confirming the deletion
        """
        db = self.get_db()
        logger.info("delete_agent_avatar_request", id=request.id)

        try:
            # Get avatar from database
            avatar = db.query(AgentAvatar).filter(AgentAvatar.id == request.id).first()

            if not avatar:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Avatar with ID {request.id} not found")
                return agent_pb2.DeleteAgentAvatarResponse(
                    success=False,
                    message=f"Avatar with ID {request.id} not found",
                )

            # Check if avatar is in use by any agents
            # This would require additional queries to check if any agents are using this avatar
            # For now, we'll just delete it

            # Delete avatar
            db.delete(avatar)
            db.commit()

            return agent_pb2.DeleteAgentAvatarResponse(
                success=True,
                message="Avatar deleted successfully",
            )
        except Exception as e:
            db.rollback()
            logger.error(f"delete_agent_avatar_failed | {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to delete avatar: {str(e)}")
            return agent_pb2.DeleteAgentAvatarResponse(
                success=False,
                message=f"Failed to delete avatar: {str(e)}",
            )
        finally:
            db.close()
