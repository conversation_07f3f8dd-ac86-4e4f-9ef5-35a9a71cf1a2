"""
Analytics Service Client

This module provides a client for communicating with the Analytics Service via gRPC.
It handles all analytics-related operations including event tracking, metrics retrieval,
dashboard analytics, webhooks, and application management.
"""

import logging
from typing import Dict, List, Optional, Any
import grpc
from grpc import RpcError

from app.core.config import settings
from app.grpc_ import analytics_pb2, analytics_pb2_grpc

logger = logging.getLogger(__name__)


class AnalyticsServiceClient:
    """Client for Analytics Service gRPC communication."""

    def __init__(self):
        """Initialize the Analytics Service client."""
        self.channel = None
        self.analytics_stub = None
        self.application_stub = None
        self._connect()

    def _connect(self):
        """Establish gRPC connection to Analytics Service."""
        try:
            analytics_address = (
                f"{settings.ANALYTICS_SERVICE_HOST}:{settings.ANALYTICS_SERVICE_PORT}"
            )
            self.channel = grpc.insecure_channel(analytics_address)
            self.analytics_stub = analytics_pb2_grpc.AnalyticsServiceStub(self.channel)
            self.application_stub = analytics_pb2_grpc.ApplicationServiceStub(self.channel)
            logger.info(f"Connected to Analytics Service at {analytics_address}")
        except Exception as e:
            logger.error(f"Failed to connect to Analytics Service: {e}")
            raise

    def close(self):
        """Close the gRPC connection."""
        if self.channel:
            self.channel.close()

    # ===== EVENT TRACKING =====

    async def track_event(
        self,
        event_type: str,
        service_type: str,
        entity_id: str,
        user_id: str,
        metadata: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Track an analytics event.

        Args:
            event_type: Type of event (usage, rating, creation, etc.)
            service_type: Type of service (mcp, workflow, agent, etc.)
            entity_id: ID of the entity being tracked
            user_id: ID of the user performing the action
            metadata: Optional JSON metadata

        Returns:
            Dict containing success status, message, and event_id
        """
        try:
            # Map string values to enum values
            event_type_enum = getattr(
                analytics_pb2.EventType,
                f"EVENT_TYPE_{event_type.upper()}",
                analytics_pb2.EventType.EVENT_TYPE_UNSPECIFIED,
            )
            service_type_enum = getattr(
                analytics_pb2.ServiceType,
                f"SERVICE_TYPE_{service_type.upper()}",
                analytics_pb2.ServiceType.SERVICE_TYPE_UNSPECIFIED,
            )

            request = analytics_pb2.TrackEventRequest(
                event_type=event_type_enum,
                service_type=service_type_enum,
                entity_id=entity_id,
                user_id=user_id,
                metadata=metadata or "",
            )

            response = self.analytics_stub.TrackEvent(request)

            return {
                "success": response.success,
                "message": response.message,
                "event_id": response.event_id,
            }

        except RpcError as e:
            logger.error(f"gRPC error tracking event: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "event_id": None}
        except Exception as e:
            logger.error(f"Error tracking event: {e}")
            return {"success": False, "message": str(e), "event_id": None}

    # ===== METRICS =====

    async def get_service_metrics(
        self, service_type: str, entity_id: str, time_period_days: int = 0
    ) -> Dict[str, Any]:
        """
        Get metrics for a specific service.

        Args:
            service_type: Type of service
            entity_id: ID of the entity
            time_period_days: Time period in days (0 for all time)

        Returns:
            Dict containing metrics data
        """
        try:
            service_type_enum = getattr(
                analytics_pb2.ServiceType,
                f"SERVICE_TYPE_{service_type.upper()}",
                analytics_pb2.ServiceType.SERVICE_TYPE_UNSPECIFIED,
            )

            request = analytics_pb2.GetServiceMetricsRequest(
                service_type=service_type_enum,
                entity_id=entity_id,
                time_period_days=time_period_days,
            )

            response = self.analytics_stub.GetServiceMetrics(request)

            metrics = response.metrics
            time_series = [
                {"date": point.date, "count": point.count} for point in metrics.usage_time_series
            ]

            return {
                "success": response.success,
                "message": response.message,
                "metrics": {
                    "entity_id": metrics.entity_id,
                    "service_type": metrics.service_type,
                    "usage_count": metrics.usage_count,
                    "average_rating": metrics.average_rating,
                    "rating_count": metrics.rating_count,
                    "usage_time_series": time_series,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting service metrics: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "metrics": None}
        except Exception as e:
            logger.error(f"Error getting service metrics: {e}")
            return {"success": False, "message": str(e), "metrics": None}

    async def get_user_activity(self, user_id: str, time_period_days: int = 0) -> Dict[str, Any]:
        """
        Get user activity data.

        Args:
            user_id: ID of the user
            time_period_days: Time period in days (0 for all time)

        Returns:
            Dict containing user activity data
        """
        try:
            request = analytics_pb2.GetUserActivityRequest(
                user_id=user_id, time_period_days=time_period_days
            )

            response = self.analytics_stub.GetUserActivity(request)

            activity = response.activity
            return {
                "success": response.success,
                "message": response.message,
                "activity": {
                    "user_id": activity.user_id,
                    "mcp_usage_count": activity.mcp_usage_count,
                    "workflow_usage_count": activity.workflow_usage_count,
                    "agent_usage_count": activity.agent_usage_count,
                    "mcp_creation_count": activity.mcp_creation_count,
                    "workflow_creation_count": activity.workflow_creation_count,
                    "agent_creation_count": activity.agent_creation_count,
                    "last_activity_date": activity.last_activity_date,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting user activity: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "activity": None}
        except Exception as e:
            logger.error(f"Error getting user activity: {e}")
            return {"success": False, "message": str(e), "activity": None}

    async def get_rating_analytics(
        self, service_type: str, entity_id: Optional[str] = None, time_period_days: int = 0
    ) -> Dict[str, Any]:
        """
        Get rating analytics.

        Args:
            service_type: Type of service
            entity_id: Optional specific entity ID
            time_period_days: Time period in days (0 for all time)

        Returns:
            Dict containing rating analytics
        """
        try:
            service_type_enum = getattr(
                analytics_pb2.ServiceType,
                f"SERVICE_TYPE_{service_type.upper()}",
                analytics_pb2.ServiceType.SERVICE_TYPE_UNSPECIFIED,
            )

            request = analytics_pb2.GetRatingAnalyticsRequest(
                service_type=service_type_enum,
                entity_id=entity_id or "",
                time_period_days=time_period_days,
            )

            response = self.analytics_stub.GetRatingAnalytics(request)

            analytics_data = response.analytics
            return {
                "success": response.success,
                "message": response.message,
                "analytics": {
                    "service_type": analytics_data.service_type,
                    "entity_id": analytics_data.entity_id,
                    "average_rating": analytics_data.average_rating,
                    "rating_count": analytics_data.rating_count,
                    "rating_distribution": dict(analytics_data.rating_distribution),
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting rating analytics: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "analytics": None}
        except Exception as e:
            logger.error(f"Error getting rating analytics: {e}")
            return {"success": False, "message": str(e), "analytics": None}

    # ===== OVERVIEW ANALYTICS =====

    async def get_overview_analytics(
        self, user_id: str, time_period_days: int = 0, application_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get overview analytics for dashboard.

        Args:
            user_id: ID of the user
            time_period_days: Time period in days (0 for all time)
            application_id: Optional application filter

        Returns:
            Dict containing overview analytics
        """
        try:
            request = analytics_pb2.GetOverviewAnalyticsRequest(
                user_id=user_id,
                time_period_days=time_period_days,
                application_id=application_id or "",
            )

            response = self.analytics_stub.GetOverviewAnalytics(request)

            analytics_data = response.analytics

            # Convert credit usage
            credit_usage = {
                "total_credits_used": analytics_data.credit_usage.total_credits_used,
                "credits_remaining": analytics_data.credit_usage.credits_remaining,
                "credits_limit": analytics_data.credit_usage.credits_limit,
                "daily_average": analytics_data.credit_usage.daily_average,
                "projected_monthly": analytics_data.credit_usage.projected_monthly,
            }

            # Convert request metrics
            request_metrics = {
                "total_requests": analytics_data.request_metrics.total_requests,
                "successful_requests": analytics_data.request_metrics.successful_requests,
                "failed_requests": analytics_data.request_metrics.failed_requests,
                "average_response_time": analytics_data.request_metrics.average_response_time,
                "request_trend": [
                    {"date": point.date, "count": point.count}
                    for point in analytics_data.request_metrics.request_trend
                ],
            }

            # Convert recent activities
            recent_activities = [
                {
                    "activity_id": activity.activity_id,
                    "activity_type": activity.activity_type,
                    "entity_id": activity.entity_id,
                    "entity_name": activity.entity_name,
                    "status": activity.status,
                    "timestamp": activity.timestamp,
                    "metadata": activity.metadata,
                }
                for activity in analytics_data.recent_activities
            ]

            # Convert time series data
            usage_trend = [
                {"date": point.date, "count": point.count} for point in analytics_data.usage_trend
            ]

            credit_trend = [
                {"date": point.date, "count": point.count} for point in analytics_data.credit_trend
            ]

            return {
                "success": response.success,
                "message": response.message,
                "analytics": {
                    "credit_usage": credit_usage,
                    "active_agents_count": analytics_data.active_agents_count,
                    "active_workflows_count": analytics_data.active_workflows_count,
                    "active_mcps_count": analytics_data.active_mcps_count,
                    "applications_count": analytics_data.applications_count,
                    "request_metrics": request_metrics,
                    "recent_activities": recent_activities,
                    "usage_trend": usage_trend,
                    "credit_trend": credit_trend,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting overview analytics: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "analytics": None}
        except Exception as e:
            logger.error(f"Error getting overview analytics: {e}")
            return {"success": False, "message": str(e), "analytics": None}

    # ===== ACTIVATION TRACKING =====

    async def track_activation(
        self,
        user_id: str,
        event_type: str,
        entity_id: Optional[str] = None,
        metadata: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Track an activation event.

        Args:
            user_id: ID of the user
            event_type: Type of activation event
            entity_id: Optional ID of related entity
            metadata: Optional JSON metadata

        Returns:
            Dict containing success status, message, and activation_id
        """
        try:
            event_type_enum = getattr(
                analytics_pb2.ActivationEventType,
                f"ACTIVATION_EVENT_TYPE_{event_type.upper()}",
                analytics_pb2.ActivationEventType.ACTIVATION_EVENT_TYPE_UNSPECIFIED,
            )

            request = analytics_pb2.TrackActivationRequest(
                user_id=user_id,
                event_type=event_type_enum,
                entity_id=entity_id or "",
                metadata=metadata or "",
            )

            response = self.analytics_stub.TrackActivation(request)

            return {
                "success": response.success,
                "message": response.message,
                "activation_id": response.activation_id,
            }

        except RpcError as e:
            logger.error(f"gRPC error tracking activation: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "activation_id": None,
            }
        except Exception as e:
            logger.error(f"Error tracking activation: {e}")
            return {"success": False, "message": str(e), "activation_id": None}

    async def get_activation_metrics(
        self, user_id: Optional[str] = None, time_period_days: int = 0
    ) -> Dict[str, Any]:
        """
        Get activation metrics.

        Args:
            user_id: Optional specific user ID (empty for all users)
            time_period_days: Time period in days (0 for all time)

        Returns:
            Dict containing activation metrics
        """
        try:
            request = analytics_pb2.GetActivationMetricsRequest(
                user_id=user_id or "", time_period_days=time_period_days
            )

            response = self.analytics_stub.GetActivationMetrics(request)

            metrics = response.metrics
            activation_trend = [
                {"date": point.date, "count": point.count} for point in metrics.activation_trend
            ]

            return {
                "success": response.success,
                "message": response.message,
                "metrics": {
                    "user_id": metrics.user_id,
                    "total_signups": metrics.total_signups,
                    "activated_users": metrics.activated_users,
                    "activation_rate": metrics.activation_rate,
                    "activation_funnel": dict(metrics.activation_funnel),
                    "activation_trend": activation_trend,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting activation metrics: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "metrics": None}
        except Exception as e:
            logger.error(f"Error getting activation metrics: {e}")
            return {"success": False, "message": str(e), "metrics": None}

    # ===== WEBHOOK MANAGEMENT =====

    async def create_webhook(
        self,
        user_id: str,
        url: str,
        event_types: List[str],
        application_id: Optional[str] = None,
        secret: Optional[str] = None,
        description: Optional[str] = None,
        is_active: bool = True,
    ) -> Dict[str, Any]:
        """
        Create a webhook.

        Args:
            user_id: ID of the user
            url: Webhook URL
            event_types: List of event types to subscribe to
            application_id: Optional application ID
            secret: Optional secret for payload verification
            description: Optional description
            is_active: Whether webhook is active

        Returns:
            Dict containing webhook data
        """
        try:
            # Convert event types to enums
            event_type_enums = []
            for event_type in event_types:
                enum_val = getattr(
                    analytics_pb2.WebhookEventType,
                    f"WEBHOOK_EVENT_TYPE_{event_type.upper()}",
                    analytics_pb2.WebhookEventType.WEBHOOK_EVENT_TYPE_UNSPECIFIED,
                )
                event_type_enums.append(enum_val)

            request = analytics_pb2.CreateWebhookRequest(
                user_id=user_id,
                application_id=application_id or "",
                url=url,
                event_types=event_type_enums,
                secret=secret or "",
                description=description or "",
                is_active=is_active,
            )

            response = self.analytics_stub.CreateWebhook(request)

            webhook = response.webhook
            return {
                "success": response.success,
                "message": response.message,
                "webhook": {
                    "id": webhook.id,
                    "user_id": webhook.user_id,
                    "application_id": webhook.application_id,
                    "url": webhook.url,
                    "event_types": list(webhook.event_types),
                    "description": webhook.description,
                    "is_active": webhook.is_active,
                    "status": webhook.status,
                    "created_at": webhook.created_at,
                    "updated_at": webhook.updated_at,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error creating webhook: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "webhook": None}
        except Exception as e:
            logger.error(f"Error creating webhook: {e}")
            return {"success": False, "message": str(e), "webhook": None}

    async def get_webhooks(
        self,
        user_id: str,
        application_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        Get webhooks for a user.

        Args:
            user_id: ID of the user
            application_id: Optional application filter
            status: Optional status filter
            limit: Maximum number of results
            offset: Offset for pagination

        Returns:
            Dict containing list of webhooks
        """
        try:
            status_enum = analytics_pb2.WebhookStatus.WEBHOOK_STATUS_UNSPECIFIED
            if status:
                status_enum = getattr(
                    analytics_pb2.WebhookStatus,
                    f"WEBHOOK_STATUS_{status.upper()}",
                    analytics_pb2.WebhookStatus.WEBHOOK_STATUS_UNSPECIFIED,
                )

            request = analytics_pb2.GetWebhooksRequest(
                user_id=user_id,
                application_id=application_id or "",
                status=status_enum,
                limit=limit,
                offset=offset,
            )

            response = self.analytics_stub.GetWebhooks(request)

            webhooks = [
                {
                    "id": webhook.id,
                    "user_id": webhook.user_id,
                    "application_id": webhook.application_id,
                    "url": webhook.url,
                    "event_types": list(webhook.event_types),
                    "description": webhook.description,
                    "is_active": webhook.is_active,
                    "status": webhook.status,
                    "created_at": webhook.created_at,
                    "updated_at": webhook.updated_at,
                }
                for webhook in response.webhooks
            ]

            return {
                "success": response.success,
                "message": response.message,
                "webhooks": webhooks,
                "total_count": response.total_count,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting webhooks: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "webhooks": [],
                "total_count": 0,
            }
        except Exception as e:
            logger.error(f"Error getting webhooks: {e}")
            return {"success": False, "message": str(e), "webhooks": [], "total_count": 0}

    async def update_webhook(
        self,
        webhook_id: str,
        user_id: str,
        url: str,
        event_types: List[str],
        secret: Optional[str] = None,
        description: Optional[str] = None,
        is_active: bool = True,
    ) -> Dict[str, Any]:
        """
        Update a webhook.

        Args:
            webhook_id: ID of the webhook
            user_id: ID of the user
            url: Webhook URL
            event_types: List of event types to subscribe to
            secret: Optional secret for payload verification
            description: Optional description
            is_active: Whether webhook is active

        Returns:
            Dict containing updated webhook data
        """
        try:
            # Convert event types to enums
            event_type_enums = []
            for event_type in event_types:
                enum_val = getattr(
                    analytics_pb2.WebhookEventType,
                    f"WEBHOOK_EVENT_TYPE_{event_type.upper()}",
                    analytics_pb2.WebhookEventType.WEBHOOK_EVENT_TYPE_UNSPECIFIED,
                )
                event_type_enums.append(enum_val)

            request = analytics_pb2.UpdateWebhookRequest(
                webhook_id=webhook_id,
                user_id=user_id,
                url=url,
                event_types=event_type_enums,
                secret=secret or "",
                description=description or "",
                is_active=is_active,
            )

            response = self.analytics_stub.UpdateWebhook(request)

            webhook = response.webhook
            return {
                "success": response.success,
                "message": response.message,
                "webhook": {
                    "id": webhook.id,
                    "user_id": webhook.user_id,
                    "application_id": webhook.application_id,
                    "url": webhook.url,
                    "event_types": list(webhook.event_types),
                    "description": webhook.description,
                    "is_active": webhook.is_active,
                    "status": webhook.status,
                    "created_at": webhook.created_at,
                    "updated_at": webhook.updated_at,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error updating webhook: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "webhook": None}
        except Exception as e:
            logger.error(f"Error updating webhook: {e}")
            return {"success": False, "message": str(e), "webhook": None}

    async def delete_webhook(self, webhook_id: str, user_id: str) -> Dict[str, Any]:
        """
        Delete a webhook.

        Args:
            webhook_id: ID of the webhook
            user_id: ID of the user

        Returns:
            Dict containing success status and message
        """
        try:
            request = analytics_pb2.DeleteWebhookRequest(webhook_id=webhook_id, user_id=user_id)

            response = self.analytics_stub.DeleteWebhook(request)

            return {"success": response.success, "message": response.message}

        except RpcError as e:
            logger.error(f"gRPC error deleting webhook: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}"}
        except Exception as e:
            logger.error(f"Error deleting webhook: {e}")
            return {"success": False, "message": str(e)}

    async def get_webhook_logs(
        self,
        webhook_id: str,
        user_id: str,
        time_period_days: int = 0,
        limit: int = 50,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        Get webhook delivery logs.

        Args:
            webhook_id: ID of the webhook
            user_id: ID of the user
            time_period_days: Time period in days (0 for all time)
            limit: Maximum number of results
            offset: Offset for pagination

        Returns:
            Dict containing webhook logs
        """
        try:
            request = analytics_pb2.GetWebhookLogsRequest(
                webhook_id=webhook_id,
                user_id=user_id,
                time_period_days=time_period_days,
                limit=limit,
                offset=offset,
            )

            response = self.analytics_stub.GetWebhookLogs(request)

            logs = [
                {
                    "id": log.id,
                    "webhook_id": log.webhook_id,
                    "event_type": log.event_type,
                    "payload": log.payload,
                    "response_status": log.response_status,
                    "response_body": log.response_body,
                    "error_message": log.error_message,
                    "timestamp": log.timestamp,
                    "retry_count": log.retry_count,
                }
                for log in response.logs
            ]

            return {
                "success": response.success,
                "message": response.message,
                "logs": logs,
                "total_count": response.total_count,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting webhook logs: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "logs": [],
                "total_count": 0,
            }
        except Exception as e:
            logger.error(f"Error getting webhook logs: {e}")
            return {"success": False, "message": str(e), "logs": [], "total_count": 0}

    # ===== DASHBOARD ANALYTICS =====

    async def get_dashboard_overview(
        self, user_id: str, time_period_days: int = 7
    ) -> Dict[str, Any]:
        """
        Get dashboard overview analytics.

        Args:
            user_id: ID of the user
            time_period_days: Time period in days (default 7)

        Returns:
            Dict containing dashboard overview data
        """
        try:
            request = analytics_pb2.GetDashboardOverviewRequest(
                user_id=user_id, time_period_days=time_period_days
            )

            response = self.analytics_stub.GetDashboardOverview(request)

            overview = response.overview

            # Convert app credit usage time series
            app_credit_usage = [
                {"timestamp": point.timestamp, "value": point.value}
                for point in overview.app_credit_usage
            ]

            # Convert agent performance data
            agent_performance = [
                {
                    "timestamp": point.timestamp,
                    "requests": point.requests,
                    "completion_rate": point.completion_rate,
                }
                for point in overview.agent_performance
            ]

            # Convert recent events
            recent_events = [
                {
                    "type": event.type,
                    "endpoint": event.endpoint,
                    "status": event.status,
                    "timestamp": event.timestamp,
                    "duration": event.duration,
                    "user": event.user,
                }
                for event in overview.recent_events
            ]

            return {
                "success": response.success,
                "message": response.message,
                "overview": {
                    "user_id": overview.user_id,
                    "active_agents": overview.active_agents,
                    "credit_usage": overview.credit_usage,
                    "agent_requests": overview.agent_requests,
                    "workflow_requests": overview.workflow_requests,
                    "custom_mcps": overview.custom_mcps,
                    "credit_breakdown": dict(overview.credit_breakdown),
                    "app_credit_usage": app_credit_usage,
                    "agent_performance": agent_performance,
                    "recent_events": recent_events,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting dashboard overview: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "overview": None}
        except Exception as e:
            logger.error(f"Error getting dashboard overview: {e}")
            return {"success": False, "message": str(e), "overview": None}

    async def get_dashboard_metrics(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get dashboard metrics.

        Args:
            user_id: Optional user-specific metrics
            days: Number of days to look back (default: 7)

        Returns:
            Dict containing dashboard metrics
        """
        try:
            request = analytics_pb2.GetDashboardMetricsRequest(user_id=user_id or "", days=days)

            response = self.analytics_stub.GetDashboardMetrics(request)

            metrics = response.metrics
            return {
                "success": response.success,
                "message": response.message,
                "metrics": {
                    "active_agents": metrics.active_agents,
                    "credit_usage": metrics.credit_usage,
                    "agent_requests": metrics.agent_requests,
                    "workflow_requests": metrics.workflow_requests,
                    "custom_mcps": metrics.custom_mcps,
                    "credit_usage_change": metrics.credit_usage_change,
                    "agent_requests_change_pct": metrics.agent_requests_change_pct,
                    "workflow_requests_change_pct": metrics.workflow_requests_change_pct,
                    "custom_mcps_change": metrics.custom_mcps_change,
                    "total_cost": metrics.total_cost,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting dashboard metrics: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "metrics": None}
        except Exception as e:
            logger.error(f"Error getting dashboard metrics: {e}")
            return {"success": False, "message": str(e), "metrics": None}

    async def get_credit_usage_breakdown(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get credit usage breakdown.

        Args:
            user_id: Optional user-specific metrics
            days: Number of days to aggregate (default: 7)

        Returns:
            Dict containing credit usage breakdown
        """
        try:
            request = analytics_pb2.GetCreditUsageBreakdownRequest(user_id=user_id or "", days=days)

            response = self.analytics_stub.GetCreditUsageBreakdown(request)

            breakdown = [
                {
                    "category": item.category,
                    "credits_used": item.credits_used,
                    "cost": item.cost,
                    "request_count": item.request_count,
                }
                for item in response.breakdown
            ]

            return {
                "success": response.success,
                "message": response.message,
                "breakdown": breakdown,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting credit usage breakdown: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "breakdown": []}
        except Exception as e:
            logger.error(f"Error getting credit usage breakdown: {e}")
            return {"success": False, "message": str(e), "breakdown": []}

    async def get_app_credit_usage(
        self, user_id: Optional[str] = None, application_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get app credit usage.

        Args:
            user_id: Optional user filter
            application_id: Optional application filter
            days: Number of days of data (default: 7)

        Returns:
            Dict containing app credit usage data
        """
        try:
            request = analytics_pb2.GetAppCreditUsageRequest(
                user_id=user_id or "", application_id=application_id or "", days=days
            )

            response = self.analytics_stub.GetAppCreditUsage(request)

            data = response.data
            timeseries = [
                {
                    "timestamp": point.timestamp,
                    "credits_used": point.credits_used,
                    "cost": point.cost,
                    "cumulative_credits": point.cumulative_credits,
                    "cumulative_cost": point.cumulative_cost,
                }
                for point in data.timeseries
            ]

            return {
                "success": response.success,
                "message": response.message,
                "data": {
                    "total_credits": data.total_credits,
                    "total_cost": data.total_cost,
                    "timeseries": timeseries,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting app credit usage: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "data": None}
        except Exception as e:
            logger.error(f"Error getting app credit usage: {e}")
            return {"success": False, "message": str(e), "data": None}

    # ===== ADDITIONAL DASHBOARD METHODS =====

    async def get_latest_api_requests(
        self, user_id: Optional[str] = None, limit: int = 50
    ) -> Dict[str, Any]:
        """
        Get latest API requests.

        Args:
            user_id: Optional user filter
            limit: Maximum number of records (default: 50)

        Returns:
            Dict containing latest API request events
        """
        try:
            request = analytics_pb2.GetLatestApiRequestsRequest(user_id=user_id or "", limit=limit)

            response = self.analytics_stub.GetLatestApiRequests(request)

            events = [
                {
                    "id": event.id,
                    "type": event.type,
                    "endpoint": event.endpoint,
                    "method": event.method,
                    "status": event.status,
                    "timestamp": event.timestamp,
                    "duration_ms": event.duration_ms,
                    "user_email": event.user_email,
                    "error_message": event.error_message,
                    "credits_used": event.credits_used,
                    "cost": event.cost,
                }
                for event in response.events
            ]

            return {"success": response.success, "message": response.message, "events": events}

        except RpcError as e:
            logger.error(f"gRPC error getting latest API requests: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "events": []}
        except Exception as e:
            logger.error(f"Error getting latest API requests: {e}")
            return {"success": False, "message": str(e), "events": []}

    async def get_agent_performance(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get agent performance metrics.

        Args:
            user_id: Optional user filter
            days: Number of days of data (default: 7)

        Returns:
            Dict containing agent performance data
        """
        try:
            request = analytics_pb2.GetAgentPerformanceRequest(user_id=user_id or "", days=days)

            response = self.analytics_stub.GetAgentPerformance(request)

            performance = [
                {
                    "agent_id": item.agent_id,
                    "agent_name": item.agent_name,
                    "total_requests": item.total_requests,
                    "successful_requests": item.successful_requests,
                    "failed_requests": item.failed_requests,
                    "success_rate": item.success_rate,
                    "avg_response_time_ms": item.avg_response_time_ms,
                    "total_credits_used": item.total_credits_used,
                    "total_cost": item.total_cost,
                    "is_active": item.is_active,
                }
                for item in response.performance
            ]

            return {
                "success": response.success,
                "message": response.message,
                "performance": performance,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting agent performance: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "performance": []}
        except Exception as e:
            logger.error(f"Error getting agent performance: {e}")
            return {"success": False, "message": str(e), "performance": []}

    async def get_workflow_utilization(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get workflow utilization metrics.

        Args:
            user_id: Optional user filter
            days: Number of days of data (default: 7)

        Returns:
            Dict containing workflow utilization data
        """
        try:
            request = analytics_pb2.GetWorkflowUtilizationRequest(user_id=user_id or "", days=days)

            response = self.analytics_stub.GetWorkflowUtilization(request)

            utilization = [
                {
                    "workflow_id": item.workflow_id,
                    "workflow_name": item.workflow_name,
                    "total_executions": item.total_executions,
                    "successful_executions": item.successful_executions,
                    "failed_executions": item.failed_executions,
                    "success_rate": item.success_rate,
                    "avg_execution_time_ms": item.avg_execution_time_ms,
                    "completion_rate_pct": item.completion_rate_pct,
                    "total_credits_used": item.total_credits_used,
                    "total_cost": item.total_cost,
                }
                for item in response.utilization
            ]

            return {
                "success": response.success,
                "message": response.message,
                "utilization": utilization,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting workflow utilization: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "utilization": []}
        except Exception as e:
            logger.error(f"Error getting workflow utilization: {e}")
            return {"success": False, "message": str(e), "utilization": []}

    async def get_system_activity(
        self, user_id: Optional[str] = None, limit: int = 10
    ) -> Dict[str, Any]:
        """
        Get system activity.

        Args:
            user_id: Optional user filter
            limit: Maximum number of records (default: 10)

        Returns:
            Dict containing system activity data
        """
        try:
            request = analytics_pb2.GetSystemActivityRequest(user_id=user_id or "", limit=limit)

            response = self.analytics_stub.GetSystemActivity(request)

            activities = [
                {
                    "id": activity.id,
                    "activity_type": activity.activity_type,
                    "title": activity.title,
                    "description": activity.description,
                    "severity": activity.severity,
                    "status": activity.status,
                    "timestamp": activity.timestamp,
                    "user_id": activity.user_id,
                    "customer_id": activity.customer_id,
                    "metadata": activity.metadata,
                }
                for activity in response.activities
            ]

            return {
                "success": response.success,
                "message": response.message,
                "activities": activities,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting system activity: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "activities": []}
        except Exception as e:
            logger.error(f"Error getting system activity: {e}")
            return {"success": False, "message": str(e), "activities": []}

    async def record_api_request(
        self,
        request_type: str,
        endpoint: str,
        status: str,
        duration_ms: int,
        user_id: str,
        user_email: str,
        method: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_data: Optional[str] = None,
        response_data: Optional[str] = None,
        error_message: Optional[str] = None,
        agent_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        application_id: Optional[str] = None,
        credits_used: float = 0.0,
        cost: float = 0.0,
    ) -> Dict[str, Any]:
        """
        Record an API request for analytics.

        Args:
            request_type: Type of request
            endpoint: API endpoint
            status: Request status
            duration_ms: Duration in milliseconds
            user_id: ID of the user
            user_email: Email of the user
            method: HTTP method
            ip_address: Optional IP address
            user_agent: Optional user agent
            request_data: Optional request data (JSON)
            response_data: Optional response data (JSON)
            error_message: Optional error message
            agent_id: Optional agent ID
            workflow_id: Optional workflow ID
            application_id: Optional application ID
            credits_used: Credits used
            cost: Cost incurred

        Returns:
            Dict containing success status and event ID
        """
        try:
            # Map string values to enum values
            request_type_enum = getattr(
                analytics_pb2.RequestType,
                f"REQUEST_TYPE_{request_type.upper()}",
                analytics_pb2.RequestType.REQUEST_TYPE_UNSPECIFIED,
            )
            status_enum = getattr(
                analytics_pb2.RequestStatus,
                f"REQUEST_STATUS_{status.upper()}",
                analytics_pb2.RequestStatus.REQUEST_STATUS_UNSPECIFIED,
            )

            request = analytics_pb2.RecordApiRequestRequest(
                request_type=request_type_enum,
                endpoint=endpoint,
                status=status_enum,
                duration_ms=duration_ms,
                user_id=user_id,
                user_email=user_email,
                method=method,
                ip_address=ip_address or "",
                user_agent=user_agent or "",
                request_data=request_data or "",
                response_data=response_data or "",
                error_message=error_message or "",
                agent_id=agent_id or "",
                workflow_id=workflow_id or "",
                application_id=application_id or "",
                credits_used=credits_used,
                cost=cost,
            )

            response = self.analytics_stub.RecordApiRequest(request)

            return {
                "success": response.success,
                "message": response.message,
                "event_id": response.event_id,
            }

        except RpcError as e:
            logger.error(f"gRPC error recording API request: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "event_id": None}
        except Exception as e:
            logger.error(f"Error recording API request: {e}")
            return {"success": False, "message": str(e), "event_id": None}

    async def record_system_activity(
        self,
        activity_type: str,
        title: str,
        description: str,
        severity: str,
        status: str,
        user_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        metadata: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Record system activity for analytics.

        Args:
            activity_type: Type of activity
            title: Activity title
            description: Activity description
            severity: Severity level
            status: Activity status
            user_id: Optional user ID
            customer_id: Optional customer ID
            metadata: Optional metadata (JSON)

        Returns:
            Dict containing success status and activity ID
        """
        try:
            request = analytics_pb2.RecordSystemActivityRequest(
                activity_type=activity_type,
                title=title,
                description=description,
                severity=severity,
                status=status,
                user_id=user_id or "",
                customer_id=customer_id or "",
                metadata=metadata or "",
            )

            response = self.analytics_stub.RecordSystemActivity(request)

            return {
                "success": response.success,
                "message": response.message,
                "activity_id": response.activity_id,
            }

        except RpcError as e:
            logger.error(f"gRPC error recording system activity: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "activity_id": None}
        except Exception as e:
            logger.error(f"Error recording system activity: {e}")
            return {"success": False, "message": str(e), "activity_id": None}


# Create a global instance
analytics_service = AnalyticsServiceClient()
