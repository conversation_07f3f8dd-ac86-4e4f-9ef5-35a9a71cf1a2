# app/services/agent_version_functions.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.agent import AgentConfig, AgentConfigVersion, AgentMarketplaceListing, AgentModelConfig, AgentKnowledgeBase
from app.grpc import agent_pb2, agent_pb2_grpc
from app.utils.constants.constants import AgentVisibilityEnum, AgentStatusEnum
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.helpers.agent_to_protobuf import _agent_version_to_protobuf
from app.helpers.agent_helpers import _create_new_version_from_agent, _create_marketplace_listing_from_agent

# Initialize structured logger
logger = structlog.get_logger()


class AgentVersionFunctions(agent_pb2_grpc.AgentServiceServicer):
    def __init__(self):
        """Initialize the AgentVersionFunctions with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def listAgentVersions(
        self, request: agent_pb2.ListAgentVersionsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListAgentVersionsResponse:
        """
        List all versions of an agent with permission checking.
        Supports both agent owners and marketplace users.
        """
        db = self.get_db()
        logger.info(
            "list_agent_versions_request",
            agent_id=request.agent_id,
            user_id=request.user_id,
            page=getattr(request, 'page', 1),
            page_size=getattr(request, 'page_size', 10),
        )

        try:
            # First, check if the agent exists
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.ListAgentVersionsResponse(
                    success=False,
                    message="Agent not found",
                    versions=[],
                    total=0,
                    page=getattr(request, 'page', 1),
                    total_pages=0,
                    current_version_id="",
                )

            # Permission check: Owner can see all versions, marketplace users can only see public agent versions
            is_owner = agent.owner_id == request.user_id
            is_public_agent = agent.visibility == AgentVisibilityEnum.PUBLIC

            if not is_owner and not is_public_agent:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "You don't have permission to view versions of this private agent"
                )
                return agent_pb2.ListAgentVersionsResponse(
                    success=False,
                    message="Permission denied: Cannot view versions of private agent",
                    versions=[],
                    total=0,
                    page=getattr(request, 'page', 1),
                    total_pages=0,
                    current_version_id="",
                )

            # Query versions for the agent
            query = db.query(AgentConfigVersion).filter(
                AgentConfigVersion.agent_config_id == request.agent_id
            )

            # Get total count
            total = query.count()

            # Apply pagination
            page = getattr(request, 'page', 1) if getattr(request, 'page', 1) > 0 else 1
            page_size = getattr(request, 'page_size', 10) if getattr(request, 'page_size', 10) > 0 else 10
            offset = (page - 1) * page_size

            # Order by created_at desc, then by id desc for consistent ordering
            versions = (
                query.order_by(AgentConfigVersion.created_at.desc(), AgentConfigVersion.id.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size

            # Convert to protobuf
            version_protos = []
            for version in versions:
                is_current = version.id == agent.current_version_id
                version_proto = _agent_version_to_protobuf(version, is_current)
                version_protos.append(version_proto)

            logger.info(
                "agent_versions_retrieved",
                agent_id=request.agent_id,
                total=total,
                page=page,
                total_pages=total_pages,
                is_owner=is_owner,
            )

            return agent_pb2.ListAgentVersionsResponse(
                success=True,
                message=f"Retrieved {len(version_protos)} versions for agent",
                versions=version_protos,
                total=total,
                page=page,
                total_pages=total_pages,
                current_version_id=agent.current_version_id or "",
            )

        except Exception as e:
            logger.error(f"Failed to list agent versions: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list agent versions: {str(e)}")
            return agent_pb2.ListAgentVersionsResponse(
                success=False,
                message=f"Failed to list agent versions: {str(e)}",
                versions=[],
                total=0,
                page=getattr(request, 'page', 1),
                total_pages=0,
                current_version_id="",
            )
        finally:
            db.close()

    def getAgentVersion(
        self, request: agent_pb2.GetAgentVersionRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetAgentVersionResponse:
        """
        Get details of a specific agent version with permission checking.
        Supports both agent owners and marketplace users.
        """
        db = self.get_db()
        logger.info(
            "get_agent_version_request",
            agent_id=request.agent_id,
            version_id=request.version_id,
            user_id=request.user_id,
        )

        try:
            # First, check if the agent exists
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.GetAgentVersionResponse(
                    success=False,
                    message="Agent not found",
                )

            # Permission check: Owner can see all versions, marketplace users can only see public agent versions
            is_owner = agent.owner_id == request.user_id
            is_public_agent = agent.visibility == AgentVisibilityEnum.PUBLIC

            if not is_owner and not is_public_agent:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "You don't have permission to view versions of this private agent"
                )
                return agent_pb2.GetAgentVersionResponse(
                    success=False,
                    message="Permission denied: Cannot view versions of private agent",
                )

            # Get the specific version
            version = (
                db.query(AgentConfigVersion)
                .filter(
                    AgentConfigVersion.agent_config_id == request.agent_id,
                    AgentConfigVersion.id == request.version_id,
                )
                .first()
            )

            if not version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Version with ID {request.version_id} not found for agent {request.agent_id}"
                )
                return agent_pb2.GetAgentVersionResponse(
                    success=False,
                    message="Version not found",
                )

            # Convert to protobuf
            is_current = version.id == agent.current_version_id
            version_proto = _agent_version_to_protobuf(version, is_current)

            logger.info(
                "agent_version_retrieved",
                agent_id=request.agent_id,
                version_id=request.version_id,
                is_owner=is_owner,
                is_current=is_current,
            )

            return agent_pb2.GetAgentVersionResponse(
                success=True,
                message=f"Retrieved version {version.version_number}",
                version=version_proto,
            )

        except Exception as e:
            logger.error(f"Failed to get agent version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get agent version: {str(e)}")
            return agent_pb2.GetAgentVersionResponse(
                success=False,
                message=f"Failed to get agent version: {str(e)}",
            )
        finally:
            db.close()

    def switchAgentVersion(
        self, request: agent_pb2.SwitchAgentVersionRequest, context: grpc.ServicerContext
    ) -> agent_pb2.SwitchAgentVersionResponse:
        """
        Switch the current version of an agent. Only owners can switch versions.
        """
        db = self.get_db()
        logger.info(
            "switch_agent_version_request",
            agent_id=request.agent_id,
            version_id=request.version_id,
            user_id=request.user_id,
        )

        try:
            # First, check if the agent exists
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.agent_id} not found")
                return agent_pb2.SwitchAgentVersionResponse(
                    success=False,
                    message="Agent not found",
                )

            # Permission check: Only owners can switch versions
            if agent.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Only the agent owner can switch versions")
                return agent_pb2.SwitchAgentVersionResponse(
                    success=False,
                    message="Permission denied: Only the agent owner can switch versions",
                )

            # Check if the target version exists
            target_version = (
                db.query(AgentConfigVersion)
                .filter(
                    AgentConfigVersion.agent_config_id == request.agent_id,
                    AgentConfigVersion.id == request.version_id,
                )
                .first()
            )

            if not target_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Version with ID {request.version_id} not found for agent {request.agent_id}"
                )
                return agent_pb2.SwitchAgentVersionResponse(
                    success=False,
                    message="Target version not found",
                )

            # Check if it's already the current version
            if agent.current_version_id == request.version_id:
                return agent_pb2.SwitchAgentVersionResponse(
                    success=True,
                    message=f"Version {target_version.version_number} is already the current version",
                    new_current_version=_agent_version_to_protobuf(target_version, True),
                )

            # Update the current version
            old_version_id = agent.current_version_id
            agent.current_version_id = request.version_id

            # Update agent fields from the target version
            agent.name = target_version.name
            agent.description = target_version.description
            agent.avatar = target_version.avatar
            agent.system_message = target_version.system_message
            agent.workflow_ids = target_version.workflow_ids
            agent.mcp_server_ids = target_version.mcp_server_ids
            agent.agent_category = target_version.agent_category
            agent.tone = target_version.tone
            agent.department = target_version.department
            agent.organization_id = target_version.organization_id
            agent.is_bench_employee = target_version.is_bench_employee
            agent.is_changes_marketplace = target_version.is_changes_marketplace
            agent.is_a2a = target_version.is_a2a
            agent.is_customizable = target_version.is_customizable
            agent.capabilities_id = target_version.capabilities_id
            agent.example_prompts = target_version.example_prompts
            agent.category = target_version.category
            agent.tags = target_version.tags
            agent.status = target_version.status

            db.commit()
            db.refresh(agent)
            db.refresh(target_version)

            logger.info(
                "agent_version_switched",
                agent_id=request.agent_id,
                old_version_id=old_version_id,
                new_version_id=request.version_id,
                user_id=request.user_id,
            )

            return agent_pb2.SwitchAgentVersionResponse(
                success=True,
                message=f"Successfully switched to version {target_version.version_number}",
                new_current_version=_agent_version_to_protobuf(target_version, True),
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to switch agent version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to switch agent version: {str(e)}")
            return agent_pb2.SwitchAgentVersionResponse(
                success=False,
                message=f"Failed to switch agent version: {str(e)}",
            )
        finally:
            db.close()

    def createVersionAndPublish(
        self, request: agent_pb2.CreateVersionAndPublishRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateVersionAndPublishResponse:
        """
        Create a new version from current agent state and optionally publish to marketplace.
        This method replaces the automatic versioning behavior with user-controlled versioning.

        Args:
            request: Contains agent_id, user_id, publish_to_marketplace flag
            context: gRPC context for error handling

        Returns:
            Response with success status, version details, and marketplace status
        """
        db = SessionLocal()
        logger.info(
            "create_version_and_publish_request",
            agent_id=request.agent_id,
            user_id=request.user_id,
            publish_to_marketplace=request.publish_to_marketplace,
        )

        try:
            # Get the agent
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent not found")
                return agent_pb2.CreateVersionAndPublishResponse(
                    success=False,
                    message="Agent not found",
                    version_created=False,
                    marketplace_updated=False,
                )

            # Check ownership
            if agent.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this agent.")
                return agent_pb2.CreateVersionAndPublishResponse(
                    success=False,
                    message="Permission denied. You are not the owner of this agent.",
                    version_created=False,
                    marketplace_updated=False,
                )

            # Check if there are pending changes to version
            if not agent.is_updated:
                return agent_pb2.CreateVersionAndPublishResponse(
                    success=True,
                    message="No pending changes to create a new version. Agent is already up to date.",
                    version_created=False,
                    marketplace_updated=False,
                )

            # Create new version from current agent state
            new_version = _create_new_version_from_agent(db, agent)
            if not new_version:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create new version")
                return agent_pb2.CreateVersionAndPublishResponse(
                    success=False,
                    message="Failed to create new version",
                    version_created=False,
                    marketplace_updated=False,
                )

            # Update agent's current version
            agent.current_version_id = new_version.id

            # Reset is_updated flag since we've created a version
            agent.is_updated = False
            agent.updated_at = datetime.now(timezone.utc)

            # Commit agent changes before marketplace operations
            db.add(agent)
            db.commit()
            db.refresh(agent)

            marketplace_updated = False
            marketplace_listing_id = ""

            # Handle marketplace publishing if requested and agent is public
            if (
                request.publish_to_marketplace
                and agent.visibility == AgentVisibilityEnum.PUBLIC
            ):
                marketplace_listing = _create_marketplace_listing_from_agent(db, agent)
                if marketplace_listing:
                    # Commit marketplace listing changes
                    db.commit()
                    db.refresh(marketplace_listing)
                    marketplace_updated = True
                    marketplace_listing_id = str(marketplace_listing.id)
                    logger.info(
                        f"Updated marketplace listing {marketplace_listing.id} with new version {new_version.id}"
                    )
                else:
                    logger.warning(
                        f"Failed to update marketplace listing for agent {agent.id}"
                    )
            elif (
                request.publish_to_marketplace
                and agent.visibility != AgentVisibilityEnum.PUBLIC
            ):
                logger.warning(
                    f"Cannot publish to marketplace: agent {agent.id} is not public"
                )

            # Refresh the new version
            db.refresh(new_version)

            # Prepare response message
            message_parts = [f"Successfully created version {new_version.version_number}"]
            if marketplace_updated:
                message_parts.append("and updated marketplace listing")
            elif (
                request.publish_to_marketplace
                and agent.visibility != AgentVisibilityEnum.PUBLIC
            ):
                message_parts.append("(marketplace update skipped - agent is not public)")

            message = " ".join(message_parts) + "."

            logger.info(
                f"Successfully created version {new_version.version_number} for agent {agent.id}, "
                f"marketplace_updated={marketplace_updated}"
            )

            return agent_pb2.CreateVersionAndPublishResponse(
                success=True,
                message=message,
                version_created=True,
                marketplace_updated=marketplace_updated,
                version_number=new_version.version_number,
                version_id=str(new_version.id),
                marketplace_listing_id=marketplace_listing_id,
            )

        except Exception as e:
            db.rollback()
            logger.error("create_version_and_publish_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return agent_pb2.CreateVersionAndPublishResponse(
                success=False,
                message=f"Failed to create version and publish: {str(e)}",
                version_created=False,
                marketplace_updated=False,
            )
        finally:
            db.close()