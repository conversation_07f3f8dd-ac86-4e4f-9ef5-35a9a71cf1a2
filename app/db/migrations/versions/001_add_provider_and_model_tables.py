"""Add provider and model tables

Revision ID: 001_provider_model
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_provider_model'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create providers table
    op.create_table('providers',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_providers_is_default'), 'providers', ['is_default'], unique=False)

    # Create models table
    op.create_table('models',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('provider_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('model_type', sa.String(length=100), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['provider_id'], ['providers.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('provider_id', 'name', name='uq_models_provider_name')
    )
    op.create_index(op.f('ix_models_provider_id'), 'models', ['provider_id'], unique=False)
    op.create_index(op.f('ix_models_is_default'), 'models', ['is_default'], unique=False)
    op.create_index(op.f('ix_models_model_type'), 'models', ['model_type'], unique=False)


def downgrade() -> None:
    # Drop models table
    op.drop_index(op.f('ix_models_model_type'), table_name='models')
    op.drop_index(op.f('ix_models_is_default'), table_name='models')
    op.drop_index(op.f('ix_models_provider_id'), table_name='models')
    op.drop_table('models')
    
    # Drop providers table
    op.drop_index(op.f('ix_providers_is_default'), table_name='providers')
    op.drop_table('providers')