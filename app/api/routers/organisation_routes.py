from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List

from app.services.organisation_service import OrganisationServiceClient
from app.grpc_ import organisation_pb2
from app.schemas.organisation import (
    OrganisationCreate,
    OrganisationResponse,
    UserOrganisationsResponse,
    InviteCreate,
    InviteResponse,
    DepartmentCreate,
    DepartmentResponse,
    DepartmentListResponse,
    OrganisationInvitesResponse,
    PendingInvite,
    CreateOrganisationResponse,
    DepartmentUser,
    DepartmentUsersResponse,
    InviterInvite,
    InviterInvitesResponse,
    SourceType,
    AddSourceRequest,
    AddSourceResponse,
    ListSourcesResponse,
    DeleteSourceRequest,
    DeleteSourceResponse,
    GrantDepartmentAccessRequest,
    GrantDepartmentAccessResponse,
    BatchGrantDepartmentAccessRequest,
    BatchGrantDepartmentAccessResponse,
    UpdateSourceCredentialsRequest,
    UpdateSourceCredentialsResponse,
    ValidateSourceRequest,
    ValidateSourceResponse,
    Folder,
    DepartmentFolders,
    ListDepartmentFoldersRequest,
    ListDepartmentFoldersResponse,
)

from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error


organisation_router = APIRouter(prefix="/organisations", tags=["organisations"])
org_service = OrganisationServiceClient()

@organisation_router.get(
    "/{organisation_id}/users",
    response_model=DepartmentUsersResponse,
    status_code=status.HTTP_200_OK,
    summary="List users in organisation or department",
    description="""
    This endpoint allows listing users within an organisation with optional department filtering and pagination.
    
    - Use department_id query parameter to filter by specific department
    - Supports pagination through page and page_size parameters
    - Returns total count and paginated results
    - Requires user to have access to the organisation
    """,
    responses={
        200: {
            "description": "Users retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Users retrieved successfully",
                        "users": [
                            {
                                "id": "user_1234567890",
                                "name": "John Doe",
                                "email": "<EMAIL>",
                                "role": "Developer",
                                "permission": "Editor"
                            }
                        ],
                        "totalCount": 10,
                        "page": 1,
                        "pageSize": 10
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid page number"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have access to this organisation"}}},
        },
        404: {
            "description": "Organisation or department not found",
            "content": {"application/json": {"example": {"detail": "Department with ID dept_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def get_organisation_users(
    organisation_id: str,
    department_id: Optional[str] = Query(None, description="Optional department ID to filter users"),
    page: int = Query(1, ge=1, description="Page number (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: dict = Depends(role_required(["user"]))
):
    """
    List users in an organisation with optional department filtering and pagination.
    
    Parameters:
    - organisation_id: ID of the organisation
    - department_id: Optional department ID to filter users (query parameter)
    - page: Page number for pagination (starts at 1)
    - page_size: Number of items per page
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - DepartmentUsersResponse: List of users with pagination info
    
    Raises:
    - HTTPException(400): If pagination parameters are invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have access to the organisation
    - HTTPException(404): If the organisation or department is not found
    - HTTPException(500): For internal server errors
    """
    try:
        # Call the organisation service with optional department_id
        users_data = await org_service.getDepartmentUsers(
            organisation_id,
            department_id,
            page=page,
            page_size=page_size
        )
        
        # Unpack the result tuple
        users_list, total_count, current_page, current_page_size, dept_name, dept_desc = users_data
        
        # Map users to schema format efficiently
        users = [
            DepartmentUser.model_validate({
                "id": user_data.get("id", ""),
                "name": user_data.get("name", ""),
                "email": user_data.get("email", ""),
                "role": user_data.get("role", ""),
                "permission": user_data.get("permission", "")
            })
            for user_data in users_list
        ]
        
        # Construct response with dynamic message
        scope = f"department '{department_id}'" if department_id else "organisation"
        message = f"Found {len(users)} of {total_count} total users in {scope} (page {current_page})"
        
        return DepartmentUsersResponse.model_validate({
            "success": True,
            "message": message,
            "users": users,
            "total_count": total_count,
            "page": current_page,
            "dept_name": dept_name,
            "dept_desc": dept_desc,
            "page_size": current_page_size
        })
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.get(
    "/{organisation_id}/departments",
    response_model=DepartmentListResponse,
    status_code=status.HTTP_200_OK,
    summary="List departments in an organisation",
    description="""
    This endpoint allows listing all departments within an organisation with optional filtering and pagination.
    
    - Supports pagination through page and page_size parameters
    - Supports filtering by search term (searches in name and description)
    - Supports filtering by specific department ID
    - Returns total count and paginated results
    """,
    responses={
        200: {
            "description": "Departments retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Departments retrieved successfully",
                        "departments": [
                            {
                                "id": "dept_1234567890",
                                "name": "Engineering",
                                "description": "Software engineering department",
                                "parentDepartmentId": None,
                                "visibility": "PRIVATE",
                                "organisationId": "org_1234567890",
                                "createdBy": "user_1234567890",
                                "createdAt": "2025-05-14T10:30:00Z",
                                "updatedAt": "2025-05-14T10:30:00Z",
                                "memberCount": 5,
                                "agentCount": 2
                            }
                        ],
                        "totalCount": 10,
                        "page": 1,
                        "pageSize": 10
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid page number"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have access to this organisation"}}},
        },
        404: {
            "description": "Organisation not found",
            "content": {"application/json": {"example": {"detail": "Organisation with ID org_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def list_departments(
    organisation_id: str,
    page: int = Query(1, ge=1, description="Page number (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    search_term: Optional[str] = Query(None, description="Optional search term to filter by name or description"),
    department_id: Optional[str] = Query(None, description="Optional specific department ID to filter by"),
    current_user: dict = Depends(role_required(["user"]))
):
    """
    List departments in an organisation with optional filtering and pagination.
    
    Parameters:
    - organisation_id: ID of the organisation to list departments for
    - page: Page number for pagination (starts at 1)
    - page_size: Number of items per page
    - search_term: Optional search term to filter departments by name or description
    - department_id: Optional specific department ID to filter by
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - DepartmentListResponse: List of departments with pagination info
    
    Raises:
    - HTTPException(400): If pagination parameters are invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have access to the organisation
    - HTTPException(404): If the organisation is not found
    - HTTPException(500): For internal server errors
    """
    try:
        requester_id = current_user["user_id"]
        # Call the organisation service
        response = await org_service.list_departments(
            organisation_id=organisation_id,
            user_id=requester_id,
            page=page,
            page_size=page_size,
            search_term=search_term,
            department_id=department_id,
        )
        
        # Map departments from gRPC response to schema format
        departments = []
        for dept in response.departments:
            # Convert visibility enum to string if present
            visibility = None
            if hasattr(dept, 'visibility'):
                visibility = dept.visibility
                
            dept_dict = {
                "id": dept.id,
                "name": dept.name,
                "description": dept.description,
                "parent_department_id": dept.parent_department_id if dept.parent_department_id else None,
                "visibility": visibility,
                "organisation_id": dept.organisation_id,
                "created_by": dept.created_by,
                "created_at": dept.created_at,
                "updated_at": dept.updated_at,
                "member_count": dept.member_count,
                "agent_count": dept.agent_count
            }
            departments.append(DepartmentResponse.model_validate(dept_dict))
        
        # Construct the final response
        result = {
            "success": True,
            "message": "Departments retrieved successfully",
            "departments": departments,
            "total_count": response.total_count,
            "page": response.page,
            "page_size": response.page_size
        }
        
        return DepartmentListResponse.model_validate(result)
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.post(
    "/create",
    response_model=CreateOrganisationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new organisation",
    description="""
    This endpoint allows authenticated users to create a new organisation.
    
    - The authenticated user automatically becomes the organisation owner.
    - User must have either 'user' or 'admin' role to create an organisation.
    - Organisation name is required and must be between 1-100 characters.
    """,
    responses={
        201: {
            "description": "Organisation created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "organisation": {
                            "id": "4ea0976b-650a-439c-b713-a53b23554037",
                            "name": "PEPSI235",
                            "websiteUrl": "pepsi.com",
                            "industry": "FMCG",
                            "createdBy": "b791efaf-f87b-445a-8762-0da0f4755465",
                            "createdAt": "2025-05-26T10:04:33.360989",
                            "updatedAt": "2025-05-26T10:04:33.360989"
                        },
                        "departments": [
                            {
                                "id": "4def7449-ac60-4d02-ad12-2e2567b4617c",
                                "name": "GENERAL",
                                "description": "General department for miscellaneous organisation activities",
                                "memberCount": 1
                            },
                            {
                                "id": "ff9aa99c-953d-43c9-bde1-f893564448fc",
                                "name": "HR",
                                "description": "Human Resources department for personnel management",
                                "memberCount": 1
                            },
                            {
                                "id": "0b7790c4-f0d6-40d5-873d-79cc636b2ce7",
                                "name": "SALES",
                                "description": "Sales department for managing customer relationships and revenue",
                                "memberCount": 1
                            }
                        ],
                        "message": "Organisation created successfully with 5 default departments",
                        "success": True
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid organisation name"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User lacks required role"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def create_organisation(
    org_data: OrganisationCreate,
    current_user: dict = Depends(role_required(["user", "admin"])) # Allow users/admins to create
):
    try:
        requester_id = current_user["user_id"]
        # Call the organisation service with updated parameter names
        response = await org_service.create_organisation(
            name=org_data.name,
            user_email=current_user["email"],
            user_name=current_user["name"],
            website_url=org_data.website_url,
            industry=org_data.industry,
            created_by=requester_id
        )
        # Extract organisation data
        org_data = {
            "id": response.organisation.organisation.id,
            "name": response.organisation.organisation.name,
            "website_url": response.organisation.organisation.website_url,
            "industry": response.organisation.organisation.industry,
            "created_by": response.organisation.organisation.created_by,
            "created_at": response.organisation.organisation.created_at,
            "updated_at": response.organisation.organisation.updated_at,
        }
        
        # Extract departments data
        departments = []
        for dept in response.organisation.departments:
            dept_dict = {
                "id": dept.id,
                "name": dept.name,
                "description": dept.description,
                "memberCount": dept.member_count,
                "visibility": dept.visibility,
                "agentCount": dept.agent_count if hasattr(dept, "agent_count") else 0
            }
            departments.append(dept_dict)
        
        # Create the complete response
        result = {
            "organisation": org_data,
            "departments": departments,
            "message": response.message,
            "success": response.success
        }
        
        # Use model_validate for Pydantic v2
        return CreateOrganisationResponse.model_validate(result)

    except HTTPException as http_exc:
        raise http_exc # Re-raise HTTP exceptions from _handle_error
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@organisation_router.post(
    "/invite",
    response_model=InviteResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Invite a user to an organisation",
    description="""
    This endpoint allows organisation owners/admins to invite users to join an organisation.
    
    - An email invitation will be sent to the specified email address.
    - Optional role and department can be specified for the invited user.
    - Optional permissions list can be provided for fine-grained access control.
    """,
    responses={
        201: {
            "description": "Invite created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "inv_1234567890",
                        "email": "<EMAIL>",
                        "organisationId": "org_1234567890",
                        "department": "Engineering",
                        "role": "Developer",
                        "permissions": "Editor",
                        "createdBy": "user_1234567890",
                        "status": "PENDING",
                        "createdAt": "2025-05-14T10:30:00Z",
                        "updatedAt": "2025-05-14T10:30:00Z"
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid email address"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User lacks required permissions"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def invite_user(
    invite_data: InviteCreate,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Create an invitation for a user to join an organisation.
    
    Parameters:
    - organisation_id: ID of the organisation to invite the user to
    - invite_data: Invitation details including email and optional role/department
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - InviteResponse: Created invitation details
    
    Raises:
    - HTTPException(400): If invitation creation fails
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user lacks required permissions
    """
    try:
        # Override the organisation ID from path parameter
        invite_data_dict = invite_data.model_dump()
        
        requester_id = current_user["user_id"]

        # Call the organisation service
        response = await org_service.invite_user(
            email=invite_data.email,
            organisation_id=invite_data_dict["organisation_id"],
            created_by=requester_id,
            role=invite_data.role,
            department=invite_data.department,
            permission=invite_data.permission
        )
        
        # Map response to schema
        invite_dict = {
            "id": response.invite.id,
            "email": response.invite.email,
            "organisationId": response.invite.organisation_id,
            "department": response.invite.department,
            "role": response.invite.role,
            "permission": response.invite.permission,
            "createdBy": response.invite.created_by,
            "status": response.invite.status,
            "createdAt": response.invite.created_at,
            "updatedAt": response.invite.updated_at
        }

        return InviteResponse.model_validate(invite_dict)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

        
@organisation_router.post(
    "/accept-invite",
    response_model=InviteResponse,
    summary="Accept organisation invitation via link",
    description="""
    This endpoint allows a user to accept an organisation invitation using an invite link.
    
    - The user must be authenticated.
    - The invite link is decoded to extract the invitation details.
    - The authenticated user's email is automatically used for verification.
    - If successful, the user is added to the organisation in the specified role.
    - The token query parameter is mandatory.
    """,
    responses={
        200: {
            "description": "Invite accepted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "inv_1234567890",
                        "email": "<EMAIL>",
                        "organisationId": "org_1234567890",
                        "department": "Engineering",
                        "role": "Developer",
                        "permissions": "READ",
                        "createdBy": "user_1234567890",
                        "status": "ACCEPTED",
                        "createdAt": "2025-05-14T10:30:00Z",
                        "updatedAt": "2025-05-14T10:30:00Z",
                        "acceptedAt": "2025-05-15T10:30:00Z",
                        "acceptedBy": "user_9876543210"
                    }
                }
            },
        },
        400: {
            "description": "Invalid invite link",
            "content": {"application/json": {"example": {"detail": "Invalid invite link"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "Email doesn't match invited user"}}},
        },
        404: {
            "description": "Invite not found",
            "content": {"application/json": {"example": {"detail": "Invite not found or expired"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def accept_invite(
    token: str = Query(..., description="The encoded invite link to accept"),
    accept: bool = Query(..., description="Accept the invitation or reject"),
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Accept an organisation invitation using an invite link.
    
    Parameters:
    - token: The encoded invite link to accept (as a query parameter)
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - InviteResponse: Accepted invitation details
    
    Raises:
    - HTTPException(400): If the invite link is invalid
    - HTTPException(401): If the user is not authenticated
    - HTTPException(403): If the user's email doesn't match the invited user
    - HTTPException(404): If the invite is not found, expired, or already processed
    - HTTPException(500): For internal server errors
    """
    try:
        # Extract user details from auth token
        user_id = current_user["user_id"]
        user_email = current_user["email"]
        user_name = current_user["name"]
        
        # Call the organisation service with the invite link and auth user details
        response = await org_service.accept_invite_by_link(
            invite_token=token,
            auth_user_id=user_id,
            user_name=user_name,
            auth_user_email=user_email,
            accept=accept if accept is not None else False
        )

        invite = response.invite
        # Map response to schema
        return InviteResponse.model_validate({
            "id": invite.id,
            "email": invite.email,
            "organisationId": invite.organisation_id,
            "department": invite.department,
            "role": invite.role,
            "permission": invite.permission,
            "createdBy": invite.created_by,
            "status": invite.status,
            "createdAt": invite.created_at,
            "updatedAt": invite.updated_at,
            "acceptedAt": getattr(invite, "accepted_at", None),
            "acceptedBy": getattr(invite, "accepted_by", None)
        })

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.post(
    "/departments",
    response_model=DepartmentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new department",
    description="""
    This endpoint allows organisation administrators to create a new department within an organisation.
    
    - Only administrators of the organisation can create departments
    - Department names must be unique within an organisation
    - Departments can be hierarchical with parent-child relationships
    - Departments can be public or private (default is PRIVATE, except for "General" which is PUBLIC)
    """,
    responses={
        201: {
            "description": "Department created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "dept_1234567890",
                        "name": "Engineering",
                        "description": "Software engineering department",
                        "parentDepartmentId": None,
                        "visibility": "PRIVATE",
                        "organisationId": "org_1234567890",
                        "createdBy": "user_1234567890",
                        "createdAt": "2025-05-14T10:30:00Z",
                        "updatedAt": "2025-05-14T10:30:00Z",
                        "memberCount": 1
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid department name"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "Only administrators can create departments"}}},
        },
        404: {
            "description": "Organisation not found",
            "content": {"application/json": {"example": {"detail": "Organisation with ID org_1234 not found"}}},
        },
        409: {
            "description": "Department already exists",
            "content": {"application/json": {"example": {"detail": "A department named 'Engineering' already exists in this organisation"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def create_department(
    department_data: DepartmentCreate,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Create a new department within an organisation.

    Parameters:
    - department_data: Department details including name, description, parent department, etc.
    - current_user: Authenticated user information (injected by dependency)

    Returns:
    - DepartmentResponse: Created department details

    Raises:
    - HTTPException(400): If department creation fails due to invalid input
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user is not an admin of the organisation
    - HTTPException(404): If the organisation is not found
    - HTTPException(409): If a department with the same name already exists
    - HTTPException(500): For internal server errors
    """
    try:
        requester_id = current_user["user_id"]
        
        # Call the organisation service
        response = await org_service.create_department(
            organisation_id=department_data.organisation_id,
            name=department_data.name,
            description=department_data.description,
            parent_department_id=department_data.parent_department_id,
            created_by=requester_id,
            visibility=department_data.visibility
        )
        
        # Map response to schema
        dept = response.department
        dept_dict = {
            "id": dept.id,
            "name": dept.name,
            "description": dept.description,
            "parent_department_id": dept.parent_department_id if dept.parent_department_id else None,
            "visibility": dept.visibility if hasattr(dept, "visibility") else None,
            "organisation_id": dept.organisation_id,
            "created_by": dept.created_by,
            "created_at": dept.created_at,
            "updated_at": dept.updated_at,
            "agent_count": dept.agent_count if hasattr(dept, "agent_count") else 0,
            "member_count": dept.member_count if hasattr(dept, "member_count") else 0
        }
        
        return DepartmentResponse.model_validate(dept_dict)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.get(
    "/getUserOrganisations",
    response_model=OrganisationInvitesResponse,
    status_code=status.HTTP_200_OK,
    summary="List organisations for current user",
    description="""
    This endpoint retrieves a list of all organisations that the authenticated user is a member of.
    
    - User must be authenticated.
    - Returns basic information about each organisation.
    """,
    responses={
        200: {
            "description": "List of organisations retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "organisations": [
                            {
                                "organisation": {
                                    "name": "AUKAIRO",
                                    "websiteUrl": "aukairo.com",
                                    "industry": "Finance",
                                    "id": "05c0239d-b9a7-4e00-9d6d-1be5f4c009d6",
                                    "createdBy": "bcbbc3cc-ccb9-4866-94a8-72266c1c269e",
                                    "createdAt": "2025-05-25T06:46:23.355366",
                                    "updatedAt": "2025-05-25T06:46:23.355366"
                                }
                            }
                        ],
                        "pendingInvites": [
                            {
                                "inviteId": "78bd363e-78fa-4d77-92ac-c5820cd3ad1e",
                                "organisation": {
                                    "name": "AUKAIRO",
                                    "websiteUrl": "aukairo.com",
                                    "industry": "Finance",
                                    "id": "05c0239d-b9a7-4e00-9d6d-1be5f4c009d6",
                                    "createdBy": "bcbbc3cc-ccb9-4866-94a8-72266c1c269e",
                                    "createdAt": "2025-05-25T06:46:23.355366",
                                    "updatedAt": "2025-05-25T06:46:23.355366"
                                },
                                "department": "christos26",
                                "role": "MEMBER",
                                "permission": "READ",
                                "inviterId": "bcbbc3cc-ccb9-4866-94a8-72266c1c269e",
                                "inviterName": "Kartik Jain",
                                "createdAt": "2025-05-25T16:08:08.821152+00:00",
                                "expiresAt": "2026-05-25T16:08:03.378216+00:00"
                            }
                        ],
                        "personalSpace": False,
                        "hasJoined": True,
                        "message": "Organisations and invites retrieved successfully",
                        "success": True
                    }
                }
            },
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def get_user_organisation(
        current_user: dict = Depends(role_required(["user"]))
    ):
    """
        List all organisations and pending invotes for the current authenticated user.
        
        Parameters:
        - current_user: Authenticated user information (injected by dependency)
        
        Returns:
        - OrganisationInvitesResponse: List of organisations with pending invites
        
        Raises:
        - HTTPException(401): If user is not authenticated
        - HTTPException(500): For internal server errors
    """
    try:
        requester_id = current_user["email"]
        # Call the organisation service
        response = await org_service.get_user_organisations(
            user_email=requester_id
        )

        validated_response = OrganisationInvitesResponse(
            organisations=[
                UserOrganisationsResponse(
                    id=org.organisation.id,
                    name=org.organisation.name,
                    website_url=org.organisation.website_url,
                    industry=org.organisation.industry,
                    created_by=org.organisation.created_by,
                    created_at=org.organisation.created_at,
                    updated_at=org.organisation.updated_at,
                    is_admin=org.is_admin,
                    is_primary=org.is_primary
                ) for org in response.organisations
            ],
            pending_invites=[
                PendingInvite(
                    invite_id=invite.invite_id,
                    organisation=OrganisationResponse(
                        id=invite.organisation.id,
                        name=invite.organisation.name,
                        website_url=invite.organisation.website_url,
                        industry=invite.organisation.industry,
                        created_by=invite.organisation.created_by,
                        created_at=invite.organisation.created_at,
                        updated_at=invite.organisation.updated_at,
                    ),
                    department=invite.department,
                    role=invite.role,
                    permission=invite.permission,
                    inviter_id=invite.inviter_id,
                    inviter_name=invite.inviter_name,
                    created_at=invite.created_at,
                    expires_at=invite.expires_at,
                ) for invite in response.pending_invites
            ],
            personal_space=response.personal_space,
            has_joined=response.has_joined,
            message=response.message,
            success=response.success
        )
        return OrganisationInvitesResponse.model_validate(validated_response)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.get(
    "/{organisation_id}/invites",
    response_model=InviterInvitesResponse,
    status_code=status.HTTP_200_OK,
    summary="Get invites sent by a user",
    description="""
    This endpoint retrieves all invites created by the authenticated user.
    
    - Supports filtering by invite status (ACCEPTED or PENDING)
    - For ACCEPTED invites, retrieves data from Neo4j (users who have joined)
    - For PENDING invites, retrieves data from PostgreSQL (invites not yet accepted)
    - Requires authentication
    """,
    responses={
        200: {
            "description": "Invites retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "invites": [
                            {
                                "invitee_id": "user_1234567890",
                                "invitee_name": "John Doe",
                                "invitee_email": "<EMAIL>",
                                "organisationId": "org_1234567890",
                                "organisationName": "Acme Inc",
                                "department": "Engineering",
                                "role": "Developer",
                                "permission": "Editor",
                                "status": "ACCEPTED",
                                "joinedAt": "2025-05-14T10:30:00Z"
                            }
                        ],
                        "message": "Found 1 accepted invites",
                        "success": True
                    }
                }
            },
        },
        400: {
            "description": "Invalid invite type",
            "content": {"application/json": {"example": {"detail": "Invalid invite type. Must be 'ACCEPTED' or 'PENDING'"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def get_invites_sent_by_user(
    organisation_id: str,
    invite_type: str = Query("PENDING", description="Type of invites to fetch: ACCEPTED or PENDING"),
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Get all invites created by the authenticated user.
    
    This endpoint fetches all invites created by a specific user (inviter).
    It can return either accepted invites (from Neo4j) or pending invites (from PostgreSQL).
    
    Parameters:
    - invite_type: Type of invites to fetch (ACCEPTED or PENDING)
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - InviterInvitesResponse: List of invites created by the user
    
    Raises:
    - HTTPException(400): If invite type is invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(500): For internal server errors
    """
    try:
        requester_id = current_user["user_id"]
        
        # Call the organisation service using the helper method
        response = await org_service.get_inviter_invites(
            user_id=requester_id,
            organisation_id=organisation_id,
            invite_type=invite_type
        )
        
        # Map the response to our schema
        invites = []
        for invite in response.invites:
            invite_dict = {
                "invitee_id": invite.invitee_id,
                "invitee_name": invite.invitee_name,
                "invitee_email": invite.invitee_email,
                "organisation_id": invite.organisation_id,
                "organisation_name": invite.organisation_name,
                "department": invite.department,
                "role": invite.role,
                "permission": invite.permission,
                "status": invite.status,
                "joined_at": invite.joined_at if hasattr(invite, "joined_at") and invite.joined_at else None,
                "created_at": invite.created_at if hasattr(invite, "created_at") and invite.created_at else None,
                "expires_at": invite.expires_at if hasattr(invite, "expires_at") and invite.expires_at else None
            }
            invites.append(InviterInvite.model_validate(invite_dict))
        
        # Construct the final response
        result = {
            "invites": invites,
            "message": response.message,
            "success": response.success
        }
        
        return InviterInvitesResponse.model_validate(result)
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.post(
    "/sources",
    response_model=AddSourceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Add a new source to organisation",
    description="""
    This endpoint allows adding a new data source (like Google Drive or Slack) to an organisation.
    
    - Requires user to have access to the organisation
    - Supports two authentication methods: OAuth credentials or Service Account key
    - At least one of credentials_file or service_account_key should be provided
    - Optional file_ids can be specified to sync only specific files
    - Source type must be one of the supported types (GOOGLE_DRIVE, SLACK)
    """,
    responses={
        201: {
            "description": "Source added successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Source added successfully",
                        "source": {
                            "id": "src_1234567890",
                            "organisationId": "org_1234567890",
                            "type": "GOOGLE_DRIVE",
                            "name": "Company Drive",
                            "createdAt": "2025-05-28T10:30:00Z",
                            "updatedAt": "2025-05-28T10:30:00Z"
                        },
                        "syncedFiles": [
                            {
                                "id": "file_1234567890",
                                "name": "Quarterly Report.pdf"
                            },
                            {
                                "id": "file_0987654321",
                                "name": "Marketing Strategy.docx"
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid source type"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have access to this organisation"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def add_source(
    source_data: AddSourceRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Add a new source with credentials to an organisation.
    
    Parameters:
    - source_data: Source details including type, name, credentials, and optional file IDs
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - AddSourceResponse: Created source details with success status
    
    Raises:
    - HTTPException(400): If source data is invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have access to the organisation
    - HTTPException(500): For internal server errors
    """
    try:
        # Convert string enum to integer for gRPC
        source_type_map = {
            "GOOGLE_DRIVE": 0,
            "SLACK": 1
        }
        source_type_int = source_type_map.get(source_data.type.value, 0)
        
        # Call the organisation service
        response = await org_service.add_source(
            organisation_id=source_data.organisation_id,
            source_type=source_type_int,
            name=source_data.name,
            credentials_file=source_data.credentials_file,
            service_account_key=source_data.service_account_key,
            file_ids=source_data.file_ids
        )
        
        # Map the gRPC response to schema format
        source_response = None
        if response.source:
            # Convert integer enum back to string for response
            source_type_str = "GOOGLE_DRIVE" if response.source.type == 0 else "SLACK"
            source_response = {
                "id": response.source.id,
                "organisation_id": response.source.organisation_id,
                "type": source_type_str,
                "name": response.source.name,
                "created_at": response.source.created_at,
                "updated_at": response.source.updated_at
            }
        
        # Map synced files if present
        synced_files = []
        if hasattr(response, 'synced_files'):
            for file_info in response.synced_files:
                synced_files.append({
                    "id": file_info.id,
                    "name": file_info.name
                })
        
        result = {
            "success": response.success,
            "message": response.message,
            "source": source_response,
            "synced_files": synced_files
        }
        
        return AddSourceResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.get(
    "/{organisation_id}/sources",
    response_model=ListSourcesResponse,
    status_code=status.HTTP_200_OK,
    summary="List sources in organisation",
    description="""
    This endpoint allows listing all data sources configured for an organisation.
    
    - Returns all sources (Google Drive, Slack, etc.) configured for the organisation
    - Requires user to have access to the organisation
    """,
    responses={
        200: {
            "description": "Sources retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Sources retrieved successfully",
                        "sources": [
                            {
                                "id": "src_1234567890",
                                "organisationId": "org_1234567890",
                                "type": "GOOGLE_DRIVE",
                                "name": "Company Drive",
                                "createdAt": "2025-05-28T10:30:00Z",
                                "updatedAt": "2025-05-28T10:30:00Z"
                            }
                        ]
                    }
                }
            },
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have access to this organisation"}}},
        },
        404: {
            "description": "Organisation not found",
            "content": {"application/json": {"example": {"detail": "Organisation with ID org_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def list_sources(
    organisation_id: str,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    List all sources for an organisation.
    
    Parameters:
    - organisation_id: ID of the organisation to list sources for
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - ListSourcesResponse: List of sources with success status
    
    Raises:
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have access to the organisation
    - HTTPException(404): If the organisation is not found
    - HTTPException(500): For internal server errors
    """
    try:
        # Call the organisation service
        response = await org_service.list_sources(organisation_id=organisation_id)
        
        # Map the gRPC response to schema format
        sources = []
        for source in response.sources:
            # Convert integer enum to string for response
            source_type_str = "GOOGLE_DRIVE" if source.type == 0 else "SLACK"
            source_dict = {
                "id": source.id,
                "organisation_id": source.organisation_id,
                "type": source_type_str,
                "name": source.name,
                "created_at": source.created_at,
                "updated_at": source.updated_at
            }
            sources.append(source_dict)
        
        result = {
            "success": response.success,
            "message": response.message,
            "sources": sources,
            "is_initial_mapping": response.isInitialMapping
        }
        
        return ListSourcesResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.delete(
    "/sources/{source_id}",
    response_model=DeleteSourceResponse,
    status_code=status.HTTP_200_OK,
    summary="Delete a source",
    description="""
    This endpoint allows deleting a data source from an organisation.
    
    - Requires user to have access to the organisation
    - Permanently removes the source and its credentials
    """,
    responses={
        200: {
            "description": "Source deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Source deleted successfully"
                    }
                }
            },
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have permission to delete this source"}}},
        },
        404: {
            "description": "Source not found",
            "content": {"application/json": {"example": {"detail": "Source with ID src_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def delete_source(
    source_id: str,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Delete a source from an organisation.
    
    Parameters:
    - source_id: ID of the source to delete
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - DeleteSourceResponse: Success status and message
    
    Raises:
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have permission to delete the source
    - HTTPException(404): If the source is not found
    - HTTPException(500): For internal server errors
    """
    try:
        requester_id = current_user["user_id"]
        
        # Call the organisation service
        response = await org_service.delete_source(
            source_id=source_id,
            user_id=requester_id
        )
        
        result = {
            "success": response.success,
            "message": response.message
        }
        
        return DeleteSourceResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.put(
    "/sources/{source_id}/credentials",
    response_model=UpdateSourceCredentialsResponse,
    status_code=status.HTTP_200_OK,
    summary="Update source credentials",
    description="""
    This endpoint allows updating credentials for an existing data source.
    
    - Requires user to have admin access to the organisation
    - Can update OAuth credentials or Service Account key
    - At least one of credentials_file or service_account_key must be provided
    """,
    responses={
        200: {
            "description": "Credentials updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Source credentials updated successfully",
                        "source": {
                            "id": "src_1234567890",
                            "organisationId": "org_1234567890",
                            "type": "GOOGLE_DRIVE",
                            "name": "Company Drive",
                            "createdAt": "2025-05-28T10:30:00Z",
                            "updatedAt": "2025-05-29T15:45:00Z"
                        }
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "No credentials provided"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have permission to update this source"}}},
        },
        404: {
            "description": "Source not found",
            "content": {"application/json": {"example": {"detail": "Source with ID src_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def update_source_credentials(
    source_id: str,
    credentials_data: UpdateSourceCredentialsRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Update credentials for an existing source.
    
    Parameters:
    - source_id: ID of the source to update
    - credentials_data: Request body containing credentials information
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - UpdateSourceCredentialsResponse: Success status, message, and updated source details
    
    Raises:
    - HTTPException(400): If no credentials are provided
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have permission to update the source
    - HTTPException(404): If the source is not found
    - HTTPException(500): For internal server errors
    """
    try:
        # Validate that at least one credential type is provided
        if not credentials_data.credentials_file and not credentials_data.service_account_key:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one of credentials_file or service_account_key must be provided"
            )
        
        user_id = current_user["user_id"]
        
        # Call the organisation service
        response = await org_service.update_source_credentials(
            source_id=source_id,
            user_id=user_id,
            credentials_file=credentials_data.credentials_file,
            service_account_key=credentials_data.service_account_key
        )
        
        # Map the gRPC response to schema format
        source_response = None
        if response.source:
            # Convert integer enum back to string for response
            source_type_str = "GOOGLE_DRIVE" if response.source.type == 0 else "SLACK"
            source_response = {
                "id": response.source.id,
                "organisation_id": response.source.organisation_id,
                "type": source_type_str,
                "name": response.source.name,
                "created_at": response.source.created_at,
                "updated_at": response.source.updated_at
            }
        
        result = {
            "success": response.success,
            "message": response.message,
            "source": source_response
        }
        
        return UpdateSourceCredentialsResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.get(
    "/sources/{source_id}/validate",
    response_model=ValidateSourceResponse,
    status_code=status.HTTP_200_OK,
    summary="Validate source and get accessible folders",
    description="""
    This endpoint validates a source and returns a list of accessible folders.
    
    - Requires user to have access to the organisation
    - Tests the source credentials and connectivity
    - Returns a list of top-level folders that are accessible with the current credentials
    - Indicates the credential type being used (oauth or service_account)
    """,
    responses={
        200: {
            "description": "Source validated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Source validated successfully",
                        "accessibleFolders": [
                            {
                                "id": "folder_1234567890",
                                "name": "Marketing Documents"
                            },
                            {
                                "id": "folder_0987654321",
                                "name": "Engineering Specs"
                            }
                        ],
                        "credentialType": "oauth"
                    }
                }
            },
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have access to this organisation"}}},
        },
        404: {
            "description": "Source not found",
            "content": {"application/json": {"example": {"detail": "Source with ID src_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def validate_source(
    source_id: str,
    organisation_id: str = Query(..., description="ID of the organisation"),
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Validate a source and get accessible folders.
    
    Parameters:
    - source_id: ID of the source to validate
    - organisation_id: ID of the organisation (query parameter)
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - ValidateSourceResponse: Success status, message, accessible folders, and credential type
    
    Raises:
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have access to the organisation
    - HTTPException(404): If the source is not found
    - HTTPException(500): For internal server errors
    """
    try:
        # Call the organisation service
        response = await org_service.validate_source(
            source_id=source_id,
            organisation_id=organisation_id
        )
        
        # Map the gRPC response to schema format
        folders = []
        for folder in response.accessible_folders:
            folder_dict = {
                "id": folder.id,
                "name": folder.name
            }
            folders.append(folder_dict)
        
        result = {
            "success": response.success,
            "message": response.message,
            "accessible_folders": folders,
            "credential_type": response.credential_type
        }
        
        return ValidateSourceResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.post(
    "/departments/grant-access",
    response_model=GrantDepartmentAccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Grant department access to files and folders",
    description="""
    This endpoint allows granting a department access to specific files and folders.
    
    - Requires admin or appropriate permissions to grant access
    - Can grant access to multiple files and folders in a single request
    - Files and folders must exist and be accessible by the requesting user
    - Department must exist within the organisation
    """,
    responses={
        200: {
            "description": "Access granted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Access granted successfully to department for 3 files and 2 folders"
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid file or folder IDs"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have permission to grant access"}}},
        },
        404: {
            "description": "Department not found",
            "content": {"application/json": {"example": {"detail": "Department with ID dept_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def grant_department_access(
    access_data: GrantDepartmentAccessRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Grant a department access to files and folders.
    
    Parameters:
    - department_id: ID of the department to grant access to
    - access_data: Request body containing file_ids and folder_ids
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - GrantDepartmentAccessResponse: Success status and message
    
    Raises:
    - HTTPException(400): If input data is invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have permission to grant access
    - HTTPException(404): If the department is not found
    - HTTPException(500): For internal server errors
    """
    try:
        user_id = current_user["user_id"]
        
        # Call the organisation service
        response = await org_service.grant_department_access(
            department_id=access_data.department_id,
            file_ids=access_data.file_ids,
            folder_ids=access_data.folder_ids,
            user_id=user_id
        )
        
        return GrantDepartmentAccessResponse.model_validate({
            "success": response.success,
            "message": response.message
        })
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.post(
    "/departments/batch-grant-access",
    response_model=BatchGrantDepartmentAccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Batch grant department access to files and folders",
    description="""
    This endpoint allows granting multiple departments access to files and folders in a batch operation.
    
    - Requires admin or appropriate permissions to grant access
    - Can grant access to multiple departments with different files/folders in a single request
    - Provides information about any departments that failed during the batch operation
    - All departments must exist within the organisation
    """,
    responses={
        200: {
            "description": "Batch access granted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Batch access granted successfully to 3 departments",
                        "failedDepartmentIds": []
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid department data"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have permission to grant access"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def batch_grant_department_access(
    batch_data: BatchGrantDepartmentAccessRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Grant multiple departments access to files and folders in a batch operation.
    
    Parameters:
    - batch_data: Request body containing list of department access data
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - BatchGrantDepartmentAccessResponse: Success status, message, and failed department IDs
    
    Raises:
    - HTTPException(400): If input data is invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have permission to grant access
    - HTTPException(500): For internal server errors
    """
    try:
        user_id = current_user["user_id"]
        
        # Convert Pydantic models to dict format for service
        department_data = []
        for dept_access in batch_data.department_data:
            department_data.append({
                "department_id": dept_access.department_id,
                "file_ids": dept_access.file_ids,
                "folder_ids": dept_access.folder_ids
            })
        
        # Call the organisation service
        response = await org_service.batch_grant_department_access(
            department_data=department_data,
            user_id=user_id
        )
        
        return BatchGrantDepartmentAccessResponse.model_validate({
            "success": response.success,
            "message": response.message,
            "failed_department_ids": list(response.failed_department_ids)
        })
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organisation_router.post(
    "/departments/folders",
    response_model=ListDepartmentFoldersResponse,
    status_code=status.HTTP_200_OK,
    summary="List folders accessible by departments",
    description="""
    This endpoint lists folders that are accessible by specified departments.
    
    - Accepts a request body with organisation ID and a list of department IDs
    - Returns a list of folders for each department
    - Useful for understanding which folders each department has access to
    - Requires user to have access to the organisation
    """,
    responses={
        200: {
            "description": "Folders retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Folders retrieved successfully",
                        "departmentFolders": [
                            {
                                "departmentId": "dept_1234567890",
                                "departmentName": "Engineering",
                                "folders": [
                                    {
                                        "id": "folder_1234567890",
                                        "name": "Engineering Documents"
                                    }
                                ]
                            }
                        ]
                    }
                }
            },
        },
        422: {
            "description": "Validation Error",
            "content": {"application/json": {"example": {"detail": "Invalid request format"}}},
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid department IDs"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User does not have access to this organisation"}}},
        },
        404: {
            "description": "Organisation or department not found",
            "content": {"application/json": {"example": {"detail": "Organisation with ID org_1234 not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def list_department_folders(
    request: ListDepartmentFoldersRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    List folders accessible by specified departments.
    
    Parameters:
    - request: ListDepartmentFoldersRequest containing organisation_id and department_ids
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - ListDepartmentFoldersResponse: Success status, message, and list of department folders
    
    Raises:
    - HTTPException(400): If input data is invalid
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user does not have access to the organisation
    - HTTPException(404): If the organisation or department is not found
    - HTTPException(500): For internal server errors
    """
    try:
        # Call the organisation service
        response = await org_service.list_department_folders(
            organisation_id=request.organisation_id,
            department_ids=request.department_ids
        )
        
        # Map the gRPC response to schema format
        department_folders = []
        for dept_folder in response.department_folders:
            folders = []
            for folder in dept_folder.folders:
                folder_dict = {
                    "id": folder.id,
                    "name": folder.name
                }
                folders.append(folder_dict)
            
            dept_folder_dict = {
                "department_id": dept_folder.department_id,
                "department_name": dept_folder.department_name,
                "folders": folders
            }
            department_folders.append(DepartmentFolders.model_validate(dept_folder_dict))
        
        result = {
            "success": response.success,
            "message": response.message,
            "department_folders": department_folders
        }
        
        return ListDepartmentFoldersResponse.model_validate(result)
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
