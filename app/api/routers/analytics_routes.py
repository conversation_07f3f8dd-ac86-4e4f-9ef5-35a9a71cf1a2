"""
Analytics Routes

This module provides FastAPI routes for analytics functionality including:
- Event tracking
- Metrics retrieval
- Dashboard analytics
- Activation tracking
- Webhook management
- System monitoring
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.analytics_service import analytics_service
from app.schemas.analytics import (
    # Event tracking
    TrackEventRequest,
    TrackEventResponse,
    # Service metrics
    ServiceMetricsResponse,
    # User activity
    UserActivityResponse,
    # Rating analytics
    RatingAnalyticsResponse,
    # Overview analytics
    OverviewAnalyticsResponse,
    # Activation tracking
    TrackActivationRequest,
    TrackActivationResponse,
    ActivationMetricsResponse,
    # Webhook management
    CreateWebhookRequest,
    UpdateWebhookRequest,
    WebhookResponse,
    GetWebhooksResponse,
    GetWebhookLogsResponse,
    # Dashboard
    DashboardOverviewResponse,
    DashboardMetricsResponse,
    CreditUsageBreakdownResponse,
    AppCreditUsageResponse,
    LatestApiRequestsResponse,
    AgentPerformanceResponse,
    WorkflowUtilizationResponse,
    SystemActivityResponse,
    # Recording
    RecordApiRequestRequest,
    RecordApiRequestResponse,
    RecordSystemActivityRequest,
    RecordSystemActivityResponse,
    # Enums
    ServiceTypeEnum,
    WebhookStatusEnum,
    BaseResponse,
)
from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error
import logging

logger = logging.getLogger(__name__)

analytics_router = APIRouter(prefix="/analytics", tags=["analytics"])


# ===== EVENT TRACKING ROUTES =====


@analytics_router.post("/events", response_model=TrackEventResponse)
async def track_event(
    request: TrackEventRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Track an analytics event.

    This endpoint allows tracking of various events like usage, ratings,
    creation, modification, etc. for different service types.
    """
    try:
        result = await analytics_service.track_event(
            event_type=request.event_type.value,
            service_type=request.service_type.value,
            entity_id=request.entity_id,
            user_id=request.user_id,
            metadata=request.metadata,
        )

        return TrackEventResponse(
            success=result["success"], message=result["message"], event_id=result.get("event_id")
        )

    except Exception as e:
        logger.error(f"Error tracking event: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== METRICS ROUTES =====


@analytics_router.get("/metrics/service", response_model=ServiceMetricsResponse)
async def get_service_metrics(
    service_type: ServiceTypeEnum = Query(..., description="Type of service"),
    entity_id: str = Query(..., description="ID of the entity"),
    time_period_days: int = Query(0, description="Time period in days (0 for all time)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get metrics for a specific service.

    Retrieves usage statistics, ratings, and time series data for a service.
    """
    try:
        result = await analytics_service.get_service_metrics(
            service_type=service_type.value, entity_id=entity_id, time_period_days=time_period_days
        )

        return ServiceMetricsResponse(
            success=result["success"], message=result["message"], metrics=result.get("metrics")
        )

    except Exception as e:
        logger.error(f"Error getting service metrics: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/activity/user/{user_id}", response_model=UserActivityResponse)
async def get_user_activity(
    user_id: str,
    time_period_days: int = Query(0, description="Time period in days (0 for all time)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get user activity data.

    Retrieves comprehensive activity statistics for a specific user.
    """
    try:
        # Check if user can access this data (users can only see their own data)
        if current_user.get("role") == "user" and current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_user_activity(
            user_id=user_id, time_period_days=time_period_days
        )

        return UserActivityResponse(
            success=result["success"], message=result["message"], activity=result.get("activity")
        )

    except Exception as e:
        logger.error(f"Error getting user activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/ratings", response_model=RatingAnalyticsResponse)
async def get_rating_analytics(
    service_type: ServiceTypeEnum = Query(..., description="Type of service"),
    entity_id: Optional[str] = Query(None, description="Specific entity ID"),
    time_period_days: int = Query(0, description="Time period in days (0 for all time)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get rating analytics.

    Retrieves rating statistics and distribution for services.
    """
    try:
        result = await analytics_service.get_rating_analytics(
            service_type=service_type.value, entity_id=entity_id, time_period_days=time_period_days
        )

        return RatingAnalyticsResponse(
            success=result["success"], message=result["message"], analytics=result.get("analytics")
        )

    except Exception as e:
        logger.error(f"Error getting rating analytics: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== OVERVIEW ANALYTICS ROUTES =====


@analytics_router.get("/overview", response_model=OverviewAnalyticsResponse)
async def get_overview_analytics(
    user_id: str = Query(..., description="User ID"),
    time_period_days: int = Query(0, description="Time period in days (0 for all time)"),
    application_id: Optional[str] = Query(None, description="Filter by application"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get overview analytics for dashboard.

    Provides comprehensive analytics overview including credits, usage, and activity.
    """
    try:
        # Check if user can access this data
        if current_user.get("role") == "user" and current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_overview_analytics(
            user_id=user_id, time_period_days=time_period_days, application_id=application_id
        )

        return OverviewAnalyticsResponse(
            success=result["success"], message=result["message"], analytics=result.get("analytics")
        )

    except Exception as e:
        logger.error(f"Error getting overview analytics: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== ACTIVATION TRACKING ROUTES =====


@analytics_router.post("/activation", response_model=TrackActivationResponse)
async def track_activation(
    request: TrackActivationRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Track an activation event.

    Records user activation milestones like signup, first agent creation, etc.
    """
    try:
        result = await analytics_service.track_activation(
            user_id=request.user_id,
            event_type=request.event_type.value,
            entity_id=request.entity_id,
            metadata=request.metadata,
        )

        return TrackActivationResponse(
            success=result["success"],
            message=result["message"],
            activation_id=result.get("activation_id"),
        )

    except Exception as e:
        logger.error(f"Error tracking activation: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/activation/metrics", response_model=ActivationMetricsResponse)
async def get_activation_metrics(
    user_id: Optional[str] = Query(None, description="Specific user ID"),
    time_period_days: int = Query(0, description="Time period in days (0 for all time)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get activation metrics.

    Retrieves activation funnel data and conversion rates.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_activation_metrics(
            user_id=user_id, time_period_days=time_period_days
        )

        return ActivationMetricsResponse(
            success=result["success"], message=result["message"], metrics=result.get("metrics")
        )

    except Exception as e:
        logger.error(f"Error getting activation metrics: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== WEBHOOK MANAGEMENT ROUTES =====


@analytics_router.post("/webhooks", response_model=WebhookResponse)
async def create_webhook(
    request: CreateWebhookRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Create a new webhook.

    Sets up webhook notifications for specified event types.
    """
    try:
        # Check if user can create webhooks for this user_id
        if current_user.get("role") == "user" and current_user.get("user_id") != request.user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        event_types = [event_type.value for event_type in request.event_types]

        result = await analytics_service.create_webhook(
            user_id=request.user_id,
            url=request.url,
            event_types=event_types,
            application_id=request.application_id,
            secret=request.secret,
            description=request.description,
            is_active=request.is_active,
        )

        return WebhookResponse(
            success=result["success"], message=result["message"], webhook=result.get("webhook")
        )

    except Exception as e:
        logger.error(f"Error creating webhook: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/webhooks", response_model=GetWebhooksResponse)
async def get_webhooks(
    user_id: str = Query(..., description="User ID"),
    application_id: Optional[str] = Query(None, description="Filter by application"),
    status: Optional[WebhookStatusEnum] = Query(None, description="Filter by status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get webhooks for a user.

    Retrieves list of webhooks with optional filtering.
    """
    try:
        # Check permissions
        if current_user.get("role") == "user" and current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_webhooks(
            user_id=user_id,
            application_id=application_id,
            status=status.value if status else None,
            limit=limit,
            offset=offset,
        )

        return GetWebhooksResponse(
            success=result["success"],
            message=result["message"],
            webhooks=result.get("webhooks", []),
            total_count=result.get("total_count", 0),
        )

    except Exception as e:
        logger.error(f"Error getting webhooks: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.put("/webhooks/{webhook_id}", response_model=WebhookResponse)
async def update_webhook(
    webhook_id: str,
    request: UpdateWebhookRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Update a webhook.

    Modifies webhook configuration including URL, event types, and settings.
    """
    try:
        # Check permissions
        if current_user.get("role") == "user" and current_user.get("user_id") != request.user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        event_types = [event_type.value for event_type in request.event_types]

        result = await analytics_service.update_webhook(
            webhook_id=webhook_id,
            user_id=request.user_id,
            url=request.url,
            event_types=event_types,
            secret=request.secret,
            description=request.description,
            is_active=request.is_active,
        )

        return WebhookResponse(
            success=result["success"], message=result["message"], webhook=result.get("webhook")
        )

    except Exception as e:
        logger.error(f"Error updating webhook: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.delete("/webhooks/{webhook_id}", response_model=BaseResponse)
async def delete_webhook(
    webhook_id: str,
    user_id: str = Query(..., description="User ID"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Delete a webhook.

    Permanently removes a webhook configuration.
    """
    try:
        # Check permissions
        if current_user.get("role") == "user" and current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.delete_webhook(webhook_id=webhook_id, user_id=user_id)

        return BaseResponse(success=result["success"], message=result["message"])

    except Exception as e:
        logger.error(f"Error deleting webhook: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/webhooks/{webhook_id}/logs", response_model=GetWebhookLogsResponse)
async def get_webhook_logs(
    webhook_id: str,
    user_id: str = Query(..., description="User ID"),
    time_period_days: int = Query(0, description="Time period in days (0 for all time)"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get webhook delivery logs.

    Retrieves logs of webhook deliveries including status and error information.
    """
    try:
        # Check permissions
        if current_user.get("role") == "user" and current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_webhook_logs(
            webhook_id=webhook_id,
            user_id=user_id,
            time_period_days=time_period_days,
            limit=limit,
            offset=offset,
        )

        return GetWebhookLogsResponse(
            success=result["success"],
            message=result["message"],
            logs=result.get("logs", []),
            total_count=result.get("total_count", 0),
        )

    except Exception as e:
        logger.error(f"Error getting webhook logs: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== DASHBOARD ROUTES =====


@analytics_router.get("/dashboard/overview", response_model=DashboardOverviewResponse)
async def get_dashboard_overview(
    user_id: str = Query(..., description="User ID"),
    time_period_days: int = Query(7, description="Time period in days"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get dashboard overview analytics.

    Provides main dashboard data including agents, credits, and recent activity.
    """
    try:
        # Check permissions
        if current_user.get("role") == "user" and current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_dashboard_overview(
            user_id=user_id, time_period_days=time_period_days
        )

        return DashboardOverviewResponse(
            success=result["success"], message=result["message"], overview=result.get("overview")
        )

    except Exception as e:
        logger.error(f"Error getting dashboard overview: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/metrics", response_model=DashboardMetricsResponse)
async def get_dashboard_metrics(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    days: int = Query(7, description="Number of days to look back"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get dashboard metrics.

    Provides detailed metrics for dashboard including changes and trends.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_dashboard_metrics(user_id=user_id, days=days)

        return DashboardMetricsResponse(
            success=result["success"], message=result["message"], metrics=result.get("metrics")
        )

    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/credit-breakdown", response_model=CreditUsageBreakdownResponse)
async def get_credit_usage_breakdown(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    days: int = Query(7, description="Number of days to aggregate"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get credit usage breakdown.

    Provides detailed breakdown of credit usage by category.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_credit_usage_breakdown(user_id=user_id, days=days)

        return CreditUsageBreakdownResponse(
            success=result["success"],
            message=result["message"],
            breakdown=result.get("breakdown", []),
        )

    except Exception as e:
        logger.error(f"Error getting credit usage breakdown: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/app-credit-usage", response_model=AppCreditUsageResponse)
async def get_app_credit_usage(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    application_id: Optional[str] = Query(None, description="Application ID filter"),
    days: int = Query(7, description="Number of days of data"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get application credit usage data.

    Provides detailed credit usage data for applications.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_app_credit_usage(
            user_id=user_id, application_id=application_id, days=days
        )

        return AppCreditUsageResponse(
            success=result["success"], message=result["message"], data=result.get("data")
        )

    except Exception as e:
        logger.error(f"Error getting app credit usage: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/latest-api-requests", response_model=LatestApiRequestsResponse)
async def get_latest_api_requests(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of records"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get latest API requests.

    Retrieves the most recent API request events.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_latest_api_requests(user_id=user_id, limit=limit)

        return LatestApiRequestsResponse(
            success=result["success"], message=result["message"], events=result.get("events", [])
        )

    except Exception as e:
        logger.error(f"Error getting latest API requests: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/agent-performance", response_model=AgentPerformanceResponse)
async def get_agent_performance(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    days: int = Query(7, description="Number of days of data"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get agent performance data.

    Provides performance metrics for agents.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_agent_performance(user_id=user_id, days=days)

        return AgentPerformanceResponse(
            success=result["success"],
            message=result["message"],
            performance=result.get("performance", []),
        )

    except Exception as e:
        logger.error(f"Error getting agent performance: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/workflow-utilization", response_model=WorkflowUtilizationResponse)
async def get_workflow_utilization(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    days: int = Query(7, description="Number of days of data"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get workflow utilization data.

    Provides utilization metrics for workflows.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_workflow_utilization(user_id=user_id, days=days)

        return WorkflowUtilizationResponse(
            success=result["success"],
            message=result["message"],
            utilization=result.get("utilization", []),
        )

    except Exception as e:
        logger.error(f"Error getting workflow utilization: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.get("/dashboard/system-activity", response_model=SystemActivityResponse)
async def get_system_activity(
    user_id: Optional[str] = Query(None, description="User ID filter"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of records"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get system activity data.

    Retrieves recent system activity events.
    """
    try:
        # Check permissions for user-specific data
        if (
            user_id
            and current_user.get("role") == "user"
            and current_user.get("user_id") != user_id
        ):
            raise HTTPException(status_code=403, detail="Access denied")

        result = await analytics_service.get_system_activity(user_id=user_id, limit=limit)

        return SystemActivityResponse(
            success=result["success"],
            message=result["message"],
            activities=result.get("activities", []),
        )

    except Exception as e:
        logger.error(f"Error getting system activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== RECORDING ROUTES =====


@analytics_router.post("/record/api-request", response_model=RecordApiRequestResponse)
async def record_api_request(
    request: RecordApiRequestRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Record an API request event.

    Records detailed information about API requests for analytics.
    """
    try:
        result = await analytics_service.record_api_request(
            request_type=request.request_type.value,
            endpoint=request.endpoint,
            status=request.status.value,
            duration_ms=request.duration_ms,
            user_id=request.user_id,
            user_email=request.user_email,
            method=request.method,
            ip_address=request.ip_address,
            user_agent=request.user_agent,
            request_data=request.request_data,
            response_data=request.response_data,
            error_message=request.error_message,
            agent_id=request.agent_id,
            workflow_id=request.workflow_id,
            application_id=request.application_id,
            credits_used=request.credits_used,
            cost=request.cost,
        )

        return RecordApiRequestResponse(
            success=result["success"], message=result["message"], event_id=result.get("event_id")
        )

    except Exception as e:
        logger.error(f"Error recording API request: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@analytics_router.post("/record/system-activity", response_model=RecordSystemActivityResponse)
async def record_system_activity(
    request: RecordSystemActivityRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Record a system activity event.

    Records system-level activities for monitoring and analytics.
    """
    try:
        result = await analytics_service.record_system_activity(
            activity_type=request.activity_type,
            title=request.title,
            description=request.description,
            severity=request.severity,
            status=request.status,
            user_id=request.user_id,
            customer_id=request.customer_id,
            metadata=request.metadata,
        )

        return RecordSystemActivityResponse(
            success=result["success"],
            message=result["message"],
            activity_id=result.get("activity_id"),
        )

    except Exception as e:
        logger.error(f"Error recording system activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
