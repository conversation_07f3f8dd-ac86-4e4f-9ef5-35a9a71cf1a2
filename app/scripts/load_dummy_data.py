"""
<PERSON><PERSON>t to load dummy data into the agent service database.
"""

import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.agent import AgentTemplate, AgentConfig
from app.models.agent_rating import AgentRating
from app.utils.dummy_data.agent_dummy_data import (
    AGENT_TEMPLATE_DUMMY_DATA,
    AGENT_CONFIG_DUMMY_DATA,
    AGENT_RATING_DUMMY_DATA,
)


def load_agent_templates(db: Session):
    """Load agent template dummy data into the database."""
    print("Loading agent templates...")
    for template_data in AGENT_TEMPLATE_DUMMY_DATA:
        # Check if template already exists
        existing_template = (
            db.query(AgentTemplate).filter(AgentTemplate.id == template_data["id"]).first()
        )

        if existing_template:
            print(f"Template {template_data['name']} already exists, skipping.")
            continue

        # Create new template
        template = AgentTemplate(**template_data)
        db.add(template)
        print(f"Added template: {template_data['name']}")

    db.commit()
    print("Agent templates loaded successfully.")


def load_agent_configs(db: Session):
    """Load agent config dummy data into the database."""
    print("Loading agent configs...")
    for config_data in AGENT_CONFIG_DUMMY_DATA:
        # Check if config already exists
        existing_config = db.query(AgentConfig).filter(AgentConfig.id == config_data["id"]).first()

        if existing_config:
            print(f"Agent config {config_data['name']} already exists, skipping.")
            continue

        # Create new config
        config = AgentConfig(**config_data)
        db.add(config)
        print(f"Added agent config: {config_data['name']}")

    db.commit()
    print("Agent configs loaded successfully.")


def load_agent_ratings(db: Session):
    """Load agent rating dummy data into the database."""
    print("Loading agent ratings...")
    for rating_data in AGENT_RATING_DUMMY_DATA:
        # Check if rating already exists
        existing_rating = db.query(AgentRating).filter(AgentRating.id == rating_data["id"]).first()

        if existing_rating:
            print(f"Rating {rating_data['id']} already exists, skipping.")
            continue

        # Create new rating
        rating = AgentRating(**rating_data)
        db.add(rating)
        print(f"Added rating for agent: {rating_data['agent_id']}")

    db.commit()
    print("Agent ratings loaded successfully.")


def load_all_dummy_data():
    """Load all dummy data into the database."""
    db = SessionLocal()
    try:
        load_agent_templates(db)
        load_agent_configs(db)
        load_agent_ratings(db)
        print("All agent service dummy data loaded successfully.")
    except Exception as e:
        print(f"Error loading dummy data: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    load_all_dummy_data()
