import os
import grpc
from concurrent import futures

from app.services.agent_service import AgentService
from app.services.provider_service import ProviderService, ModelService
from app.grpc import agent_pb2_grpc, provider_pb2_grpc


def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    # Add agent service to server
    agent_service = AgentService()
    agent_pb2_grpc.add_AgentServiceServicer_to_server(agent_service, server)
    
    # Add provider services to server
    provider_service = ProviderService()
    model_service = ModelService()
    provider_pb2_grpc.add_ProviderServiceServicer_to_server(provider_service, server)
    provider_pb2_grpc.add_ModelServiceServicer_to_server(model_service, server)

    # Get port from environment or use default
    port = os.getenv('PORT', '50057')  # Changed to a different default port (50057) to avoid collision.
    server.add_insecure_port(f'[::]:{port}')

    # Start server
    server.start()
    print(f"Agent service started on port {port}")

    # Keep thread alive
    server.wait_for_termination()

if __name__ == '__main__':
    serve()