from app.grpc import agent_pb2, agent_pb2_grpc
from app.models.agent import AgentCapabilities, AgentConfig, AgentConfigVersion, AgentAvatar, AgentMarketplaceListing
import json
import structlog
from sqlalchemy.orm import Session, joinedload
from app.utils.constants.constants import AgentStatusEnum, AgentVisibilityEnum
from google.protobuf.timestamp_pb2 import Timestamp
from typing import List

logger = structlog.get_logger()


def _agent_avatar_to_protobuf(avatar: AgentAvatar) -> agent_pb2.AgentAvatar:
    """Convert AgentAvatar model to protobuf AgentAvatar message"""
    # Handle both string and datetime objects for created_at and updated_at
    created_at = avatar.created_at
    if hasattr(created_at, "isoformat"):
        created_at = created_at.isoformat()

    updated_at = avatar.updated_at
    if hasattr(updated_at, "isoformat"):
        updated_at = updated_at.isoformat()

    return agent_pb2.AgentAvatar(
        id=str(avatar.id),
        url=avatar.url,
        created_at=created_at,
        updated_at=updated_at,
    )


def _agent_to_protobuf(db: Session, agent: AgentConfig) -> agent_pb2.Agent:
    """Convert AgentConfig model to protobuf Agent message"""
    try:
        # Get current version to retrieve model config and knowledge base
        current_version = None
        model_provider = ""
        model_name = ""
        files = []
        urls = []
        temperature = 0.0 # Initialize temperature with a default value
        max_tokens = 0 # Initialize max_tokens with a default value

        if agent.current_version_id:
            current_version = (
                db.query(AgentConfigVersion)
                .options(
                    joinedload(AgentConfigVersion.model_config),
                    joinedload(AgentConfigVersion.knowledge_base)
                )
                .filter(AgentConfigVersion.id == agent.current_version_id)
                .first()
            )
            
            if current_version:
                # Get model config from current version
                if current_version.model_config_id and current_version.model_config:
                    model_provider = current_version.model_config.model_provider or ""
                    model_name = current_version.model_config.model_name or ""
                    # temperature and max_tokens are already initialized, update if found
                    temperature = current_version.model_config.temperature if current_version.model_config.temperature is not None else temperature
                    max_tokens = current_version.model_config.max_tokens if current_version.model_config.max_tokens is not None else max_tokens

                # Get knowledge base from current version
                if current_version.knowledge_base_id and current_version.knowledge_base:
                    files = current_version.knowledge_base.files or []
                    urls = current_version.knowledge_base.urls or []
        
        # Fallback to legacy fields if no current version (for backward compatibility)
        if not current_version:
            model_provider = agent.model_provider or ""
            model_name = agent.model_name or ""
            # temperature and max_tokens are already initialized, update if found
            temperature = agent.temperature if agent.temperature is not None else temperature
            max_tokens = agent.max_tokens if agent.max_tokens is not None else max_tokens
            files = agent.files if agent.files else []
            urls = agent.urls if agent.urls else []

        agent_proto = agent_pb2.Agent(
            id=str(agent.id),
            name=agent.name or "",
            description=agent.description or "",
            avatar=agent.avatar or "",
            owner_id=agent.owner_id or "",
            user_ids=agent.user_ids if agent.user_ids else [],
            owner_type=agent.owner_type or "",
            # Make sure these fields are explicitly set with default values if None
            is_bench_employee=bool(agent.is_bench_employee),  # Convert to bool explicitly
            is_imported=bool(agent.is_imported),
            # Rest of the fields...
            agent_category=agent.agent_category or "",
            system_message=agent.system_message or "",
            model_provider=model_provider,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            workflow_ids=agent.workflow_ids if agent.workflow_ids else [],
            mcp_server_ids=agent.mcp_server_ids if agent.mcp_server_ids else [],
            agent_topic_type=agent.agent_topic_type or "",
            visibility=agent.visibility or "",
            tags=agent.tags if agent.tags else [],
            status=agent.status or "",
            created_at=agent.created_at.isoformat(),
            updated_at=agent.updated_at.isoformat(),
            department=agent.department or "",
            organization_id=agent.organization_id or "",
            tone=agent.tone or "",
            files=[
                agent_pb2.FileEntry(file=f.get("file", ""), created_at=f.get("created_at", ""), size=f.get("size", 0))
                for f in files
            ],
            urls=[
                agent_pb2.UrlEntry(url=u.get("url", ""), created_at=u.get("created_at", ""))
                for u in urls
            ],
            is_changes_marketplace=bool(agent.is_changes_marketplace) if agent.is_changes_marketplace is not None else False,
            # New fields
            is_a2a=bool(agent.is_a2a) if agent.is_a2a is not None else False,
            is_customizable=(
                bool(agent.is_customizable) if agent.is_customizable is not None else False
            ),
            capabilities_id=str(agent.capabilities_id) if agent.capabilities_id else "",
            example_prompts=agent.example_prompts if agent.example_prompts else [],
            category=agent.category or "",
            is_updated = agent.is_updated if agent.is_updated else False
        )
        if agent.capabilities_id:
            # agent_proto.capabilities_id is already set during initialization
            db_capabilities = (
                db.query(AgentCapabilities)
                .filter(AgentCapabilities.id == agent.capabilities_id)
                .first()
            )
            if db_capabilities:
                capabilities_proto = agent_pb2.AgentCapabilities(
                    id=str(db_capabilities.id),
                    capabilities=(
                        json.dumps(db_capabilities.capabilities)
                        if db_capabilities.capabilities
                        else ""
                    ),
                    input_modes=db_capabilities.input_modes if db_capabilities.input_modes else [],
                    output_modes=(
                        db_capabilities.output_modes if db_capabilities.output_modes else []
                    ),
                    response_model=(
                        db_capabilities.response_model if db_capabilities.response_model else []
                    ),
                    created_at=(
                        db_capabilities.created_at.isoformat() if db_capabilities.created_at else ""
                    ),
                    updated_at=(
                        db_capabilities.updated_at.isoformat() if db_capabilities.updated_at else ""
                    ),
                )
                agent_proto.agent_capabilities.CopyFrom(capabilities_proto)

        if agent.variables:  # agent.variables is List[AgentVariables (ORM)]
            logger.debug(f"ORM agent has {len(agent.variables)} variables. Converting to proto.")
            for db_var_orm_item in agent.variables:
                # Convert Python VariableTypeEnum (e.g., db_var_orm_item.type.value which is "text")
                # to Protobuf VariableType enum (int)
                var_type_proto_int_val = db_var_orm_item.type

                var_data_proto_item = agent_pb2.AgentVariableData(
                    id=str(db_var_orm_item.id),  # Include ID in response
                    name=db_var_orm_item.name,
                    type=var_type_proto_int_val,
                    created_at=db_var_orm_item.created_at.isoformat(),
                    updated_at=db_var_orm_item.updated_at.isoformat(),
                )
                if db_var_orm_item.description is not None:
                    var_data_proto_item.description = db_var_orm_item.description
                if (
                    db_var_orm_item.default_value is not None
                ):  # default_value is already string from DB
                    var_data_proto_item.default_value = db_var_orm_item.default_value

                agent_proto.variables.append(var_data_proto_item)
        return agent_proto
    except Exception as e:
        logger.error(f"Error converting AgentConfig to protobuf: {e}")
        raise e


def _agent_version_to_protobuf(version: AgentConfigVersion, is_current: bool = False) -> agent_pb2.AgentVersion:
    """Convert AgentConfigVersion model to protobuf AgentVersion message"""
    try:
        version_proto = agent_pb2.AgentVersion(
            id=str(version.id),
            agent_config_id=str(version.agent_config_id),
            version_number=version.version_number,
            name=version.name,
            description=version.description,
            avatar=version.avatar,
            agent_category=version.agent_category,
            system_message=version.system_message,
            workflow_ids=version.workflow_ids if version.workflow_ids else [],
            mcp_server_ids=version.mcp_server_ids if version.mcp_server_ids else [],
            agent_topic_type=version.agent_topic_type if version.agent_topic_type else "",
            department=version.department if version.department else "",
            organization_id=version.organization_id if version.organization_id else "",
            tone=version.tone if version.tone else "",
            is_bench_employee=bool(version.is_bench_employee),
            is_changes_marketplace=bool(version.is_changes_marketplace) if version.is_changes_marketplace is not None else False,
            is_a2a=bool(version.is_a2a),
            is_customizable=bool(version.is_customizable),
            capabilities_id=str(version.capabilities_id) if version.capabilities_id else "",
            example_prompts=version.example_prompts if version.example_prompts else [],
            category=version.category if version.category else "",
            tags=version.tags if version.tags else [],
            status=version.status,
            version_notes=version.version_notes if version.version_notes else "",
            is_current=is_current,
            created_at=version.created_at.isoformat(),
            updated_at=version.updated_at.isoformat(),
        )

        # Add model config if available
        if version.model_config_id and version.model_config:
            model_config_proto = agent_pb2.AgentModelConfig(
                id=str(version.model_config.id),
                model_provider=version.model_config.model_provider if version.model_config.model_provider else "",
                model_name=version.model_config.model_name if version.model_config.model_name else "",
                temperature=version.model_config.temperature if version.model_config.temperature else 0.0,
                max_tokens=version.model_config.max_tokens if version.model_config.max_tokens else 0,
                created_at=version.model_config.created_at.isoformat(),
                updated_at=version.model_config.updated_at.isoformat(),
            )
            version_proto.model_config.CopyFrom(model_config_proto)

        # Add knowledge base if available
        if version.knowledge_base_id and version.knowledge_base:
            knowledge_base_proto = agent_pb2.AgentKnowledgeBase(
                id=str(version.knowledge_base.id),
                files=version.knowledge_base.files if version.knowledge_base.files else [],
                urls=version.knowledge_base.urls if version.knowledge_base.urls else [],
                created_at=version.knowledge_base.created_at.isoformat(),
                updated_at=version.knowledge_base.updated_at.isoformat(),
            )
            version_proto.knowledge_base.CopyFrom(knowledge_base_proto)

        return version_proto
    except Exception as e:
        logger.error(f"Error converting AgentConfigVersion to protobuf: {e}")
        raise e


def _ensure_agent_template_from_agent_config(
    db, agent_config: AgentConfig, create_new_if_missing: bool = True
):
    """
    Legacy function for template management - now deprecated.
    This function is kept for backward compatibility but does nothing.
    """
    logger.warning(
        "_ensure_agent_template_from_agent_config is deprecated - AgentTemplate model removed"
    )
    return None

def _marketplace_listing_to_protobuf(listing: AgentMarketplaceListing) -> agent_pb2.MarketplaceListing:
    """Convert AgentMarketplaceListing to protobuf MarketplaceListing."""
    return agent_pb2.MarketplaceListing(
        id=str(listing.id),
        agent_version_id=str(listing.agent_config_version_id),
        title=listing.title or "",
        description=listing.description or "",
        tags=listing.tags or [],
        category=listing.category or "",
        is_featured=listing.is_featured or False,
        download_count=listing.download_count or 0,
        rating=listing.rating or 0.0,
        created_at=listing.created_at.isoformat() if listing.created_at else "",
        updated_at=listing.updated_at.isoformat() if listing.updated_at else ""
    )


def _agent_version_list_to_protobuf(versions: List[AgentConfigVersion], current_version_id: str = None) -> List[agent_pb2.AgentVersion]:
    """Convert list of AgentConfigVersion to protobuf AgentVersion list."""
    return [
        _agent_version_to_protobuf(version, is_current=(str(version.id) == current_version_id))
        for version in versions
    ]

