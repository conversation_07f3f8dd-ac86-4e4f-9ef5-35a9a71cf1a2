from enum import Enum


class AgentOwnerTypeEnum(str, Enum):
    USER = "user"
    ENTERPRISE = "enterprise"
    PLATFORM = "platform"


class AgentCategoryEnum(str, Enum):
    USER_PROXY = "user_proxy"
    ASSISTANT = "assistant"
    AI_AGENT = "ai_agent"


class AgentVisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class AgentStatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"


class AgentCategoryTypeEnum(str, Enum):
    MARKETING = "marketing"


class CategoryEnum(str, Enum):
    ENGINEERING = "engineering"
    MARKETING = "marketing"
    SALES = "sales"
    CUSTOMER_SUPPORT = "customer_support"
    HUMAN_RESOURCES = "human_resources"
    FINANCE = "finance"
    OPERATIONS = "operations"
    GENERAL = "general"


class AgentToneEnum(str, Enum):
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"
    CASUAL = "casual"
    FORMAL = "formal"
    ENTHUSIASTIC = "enthusiastic"


class MarketplaceItemSortEnum(str, Enum):
    NEWEST = "newest"
    OLDEST = "oldest"
    MOST_POPULAR = "most_popular"
    HIGHEST_RATED = "highest_rated"


class InputModeEnum(str, Enum):
    TEXT = "text"
    VOICE = "voice"
    IMAGE = "image"
    FILE_UPLOAD = "file_upload"


class OutputModeEnum(str, Enum):
    TEXT = "text"
    VOICE = "voice"
    IMAGE = "image"
    FILE_GENERATION = "file_generation"


class ResponseModelEnum(str, Enum):
    STREAMING_RESPONSE = "streaming_response"
    PUSH_NOTIFICATION = "push_notification"
    STATE_TRANSITION_HISTORY = "state_transition_history"


class VariableTypeEnum(str, Enum):
    TEXT = "text"
    NUMBER = "number"
    JSON = "json"
