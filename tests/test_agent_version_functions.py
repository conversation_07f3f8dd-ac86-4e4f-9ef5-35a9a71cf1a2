import unittest
from unittest.mock import MagicMock, patch
import grpc
from datetime import datetime, timezone
from app.services.agent_version_functions import AgentVersionFunctions
from app.models.agent import AgentConfig, AgentConfigVersion, AgentMarketplaceListing, AgentModelConfig, AgentKnowledgeBase
from app.utils.constants.constants import Agent<PERSON>tatusEnum, AgentVisibilityEnum
from app.grpc import agent_pb2


class MockRequest:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class TestAgentVersionFunctions(unittest.TestCase):
    def setUp(self):
        self.agent_version_functions = AgentVersionFunctions()
        
        # Mock the database session
        self.db_mock = MagicMock()
        
        # Mock get_db to return our mock and handle the context manager
        def mock_get_db():
            return self.db_mock
        
        self.agent_version_functions.get_db = mock_get_db
        
        # Mock the close method
        self.db_mock.close = MagicMock()
        
        # Mock gRPC context
        self.context_mock = MagicMock()
        
        # Test data
        self.agent_id = "test-agent-id"
        self.version_id = "test-version-id"
        self.user_id = "test-user-id"
        self.other_user_id = "other-user-id"
        
        # Mock agent with all required fields
        self.agent_mock = MagicMock(spec=AgentConfig)
        self.agent_mock.id = self.agent_id
        self.agent_mock.name = "Test Agent"
        self.agent_mock.description = "Test Description"
        self.agent_mock.avatar = "test-avatar.png"
        self.agent_mock.owner_id = self.user_id
        self.agent_mock.user_ids = []
        self.agent_mock.owner_type = "user"
        self.agent_mock.is_bench_employee = False
        self.agent_mock.is_imported = False
        self.agent_mock.agent_category = "general"
        self.agent_mock.system_message = "Test system message"
        self.agent_mock.model_provider = "openai"
        self.agent_mock.model_name = "gpt-4"
        self.agent_mock.model_api_key = "test-key"
        self.agent_mock.workflow_ids = ["workflow-1", "workflow-2"]
        self.agent_mock.mcp_server_ids = ["mcp-1", "mcp-2"]
        self.agent_mock.agent_topic_type = "general"
        self.agent_mock.visibility = AgentVisibilityEnum.PUBLIC
        self.agent_mock.tags = ["tag1", "tag2"]
        self.agent_mock.status = AgentStatusEnum.ACTIVE
        self.agent_mock.created_at = datetime.now(timezone.utc)
        self.agent_mock.updated_at = datetime.now(timezone.utc)
        self.agent_mock.department = "engineering"
        self.agent_mock.organization_id = "test-org"
        self.agent_mock.tone = "professional"
        self.agent_mock.ruh_credentials = False
        self.agent_mock.files = ["file1.pdf", "file2.txt"]
        self.agent_mock.urls = ["https://example.com"]
        self.agent_mock.is_changes_marketplace = False
        self.agent_mock.is_a2a = False
        self.agent_mock.is_customizable = True
        self.agent_mock.capabilities_id = "cap-123"
        self.agent_mock.example_prompts = ["Example prompt 1", "Example prompt 2"]
        self.agent_mock.category = "general"
        self.agent_mock.variables = []
        self.agent_mock.is_updated = True
        self.agent_mock.current_version_id = self.version_id
        
        # Mock model config
        self.model_config_mock = MagicMock(spec=AgentModelConfig)
        self.model_config_mock.id = "model-config-123"
        self.model_config_mock.model_provider = "openai"
        self.model_config_mock.model_name = "gpt-4"
        self.model_config_mock.model_api_key = "test-key"
        self.model_config_mock.created_at = datetime.now(timezone.utc)
        self.model_config_mock.updated_at = datetime.now(timezone.utc)
        
        # Mock knowledge base
        self.knowledge_base_mock = MagicMock(spec=AgentKnowledgeBase)
        self.knowledge_base_mock.id = "kb-123"
        self.knowledge_base_mock.files = ["file1.pdf", "file2.txt"]
        self.knowledge_base_mock.urls = ["https://example.com"]
        self.knowledge_base_mock.created_at = datetime.now(timezone.utc)
        self.knowledge_base_mock.updated_at = datetime.now(timezone.utc)
        
        # Mock version with all required fields
        self.version_mock = MagicMock(spec=AgentConfigVersion)
        self.version_mock.id = self.version_id
        self.version_mock.agent_config_id = self.agent_id
        self.version_mock.version_number = "1.0.0"
        self.version_mock.name = "Test Agent"
        self.version_mock.description = "Test Description"
        self.version_mock.avatar = "test-avatar.png"
        self.version_mock.agent_category = "general"
        self.version_mock.system_message = "Test system message"
        self.version_mock.workflow_ids = ["workflow-1", "workflow-2"]
        self.version_mock.mcp_server_ids = ["mcp-1", "mcp-2"]
        self.version_mock.agent_topic_type = "general"
        self.version_mock.department = "engineering"
        self.version_mock.organization_id = "test-org"
        self.version_mock.ruh_credentials = False
        self.version_mock.tone = "professional"
        self.version_mock.is_bench_employee = False
        self.version_mock.is_changes_marketplace = False
        self.version_mock.is_a2a = False
        self.version_mock.is_customizable = True
        self.version_mock.capabilities_id = "cap-123"
        self.version_mock.example_prompts = ["Example prompt 1", "Example prompt 2"]
        self.version_mock.category = "general"
        self.version_mock.tags = ["tag1", "tag2"]
        self.version_mock.status = AgentStatusEnum.ACTIVE
        self.version_mock.version_notes = "Initial version"
        self.version_mock.model_config_id = "model-config-123"
        self.version_mock.model_config = self.model_config_mock
        self.version_mock.knowledge_base_id = "kb-123"
        self.version_mock.knowledge_base = self.knowledge_base_mock
        self.version_mock.created_at = datetime.now(timezone.utc)
        self.version_mock.updated_at = datetime.now(timezone.utc)

    def test_list_agent_versions_success_owner(self):
        """Test listing agent versions successfully as owner"""
        # Setup
        request = agent_pb2.ListAgentVersionsRequest(
            agent_id=self.agent_id,
            user_id=self.user_id,
            page=1,
            page_size=10
        )

        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock

        # Mock versions query
        versions_query = MagicMock()
        versions_query.filter.return_value = versions_query
        versions_query.count.return_value = 1
        versions_query.order_by.return_value = versions_query
        versions_query.offset.return_value = versions_query
        versions_query.limit.return_value = versions_query
        versions_query.all.return_value = [self.version_mock]

        # Set up side_effect for different query calls
        self.db_mock.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=self.agent_mock)))),
            versions_query,
            versions_query
        ]

        # Execute
        with patch('app.services.agent_version_functions._agent_version_to_protobuf') as mock_protobuf:
            # Create a real protobuf object instead of MagicMock
            mock_version_proto = agent_pb2.AgentVersion(
                id=self.version_id,
                version_number="1.0.0",
                is_current=True
            )
            mock_protobuf.return_value = mock_version_proto

            response = self.agent_version_functions.listAgentVersions(request, self.context_mock)

        # Assert all response fields
        self.assertTrue(response.success)
        self.assertIn("Retrieved 1 versions", response.message)
        self.assertEqual(len(response.versions), 1)
        self.assertEqual(response.total, 1)
        self.assertEqual(response.page, 1)
        self.assertEqual(response.total_pages, 1)
        self.assertEqual(response.current_version_id, self.version_id)

        # Verify version protobuf conversion was called
        mock_protobuf.assert_called_once_with(self.version_mock, True)

    def test_list_agent_versions_success_public_agent(self):
        """Test listing agent versions for public agent by non-owner"""
        # Setup
        request = agent_pb2.ListAgentVersionsRequest(
            agent_id=self.agent_id,
            user_id=self.other_user_id,  # Different user
            page=1,
            page_size=5
        )
        
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        
        # Mock versions query
        versions_query = MagicMock()
        versions_query.filter.return_value = versions_query
        versions_query.count.return_value = 2
        versions_query.order_by.return_value = versions_query
        versions_query.offset.return_value = versions_query
        versions_query.limit.return_value = versions_query
        versions_query.all.return_value = [self.version_mock, self.version_mock]
        
        # Set up side_effect for different query calls
        self.db_mock.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=self.agent_mock)))),
            versions_query,
            versions_query
        ]
        
        # Execute
        with patch('app.utils.helpers.agent_to_protobuf._agent_version_to_protobuf') as mock_protobuf:
            mock_version_proto = MagicMock()
            mock_protobuf.return_value = mock_version_proto
            
            response = self.agent_version_functions.listAgentVersions(request, self.context_mock)
        
        # Assert
        self.assertTrue(response.success)
        self.assertEqual(len(response.versions), 2)
        self.assertEqual(response.total, 2)
        self.assertEqual(response.page, 1)
        self.assertEqual(response.total_pages, 1)  # 2 items, 5 per page = 1 page

    def test_list_agent_versions_permission_denied_private_agent(self):
        """Test permission denied for private agent by non-owner"""
        # Setup
        request = agent_pb2.ListAgentVersionsRequest(
            agent_id=self.agent_id,
            user_id=self.other_user_id,  # Different user
            page=1,
            page_size=10
        )
        
        # Make agent private
        self.agent_mock.visibility = AgentVisibilityEnum.PRIVATE
        
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        
        # Execute
        response = self.agent_version_functions.listAgentVersions(request, self.context_mock)
        
        # Assert
        self.assertFalse(response.success)
        self.assertIn("Permission denied", response.message)
        self.assertEqual(len(response.versions), 0)
        self.assertEqual(response.total, 0)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    def test_list_agent_versions_agent_not_found(self):
        """Test agent not found"""
        # Setup
        request = agent_pb2.ListAgentVersionsRequest(
            agent_id="non-existent-id",
            user_id=self.user_id,
            page=1,
            page_size=10
        )
        
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = None
        
        # Execute
        response = self.agent_version_functions.listAgentVersions(request, self.context_mock)
        
        # Assert
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Agent not found")
        self.assertEqual(len(response.versions), 0)
        self.assertEqual(response.total, 0)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    def test_list_agent_versions_pagination(self):
        """Test pagination functionality"""
        # Setup
        request = agent_pb2.ListAgentVersionsRequest(
            agent_id=self.agent_id,
            user_id=self.user_id,
            page=2,
            page_size=3
        )
        
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        
        # Mock versions query with 7 total items
        versions_query = MagicMock()
        versions_query.filter.return_value = versions_query
        versions_query.count.return_value = 7
        versions_query.order_by.return_value = versions_query
        versions_query.offset.return_value = versions_query
        versions_query.limit.return_value = versions_query
        versions_query.all.return_value = [self.version_mock, self.version_mock, self.version_mock]
        
        # Set up side_effect for different query calls
        self.db_mock.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=self.agent_mock)))),
            versions_query,
            versions_query
        ]
        
        # Execute
        with patch('app.utils.helpers.agent_to_protobuf._agent_version_to_protobuf') as mock_protobuf:
            mock_version_proto = MagicMock()
            mock_protobuf.return_value = mock_version_proto
            
            response = self.agent_version_functions.listAgentVersions(request, self.context_mock)
        
        # Assert pagination
        self.assertTrue(response.success)
        self.assertEqual(len(response.versions), 3)
        self.assertEqual(response.total, 7)
        self.assertEqual(response.page, 2)
        self.assertEqual(response.total_pages, 3)  # 7 items, 3 per page = 3 pages
        
        # Verify offset was calculated correctly (page 2, size 3 = offset 3)
        versions_query.offset.assert_called_with(3)
        versions_query.limit.assert_called_with(3)

    def test_get_agent_version_success(self):
        """Test getting a specific agent version successfully"""
        # Setup
        request = agent_pb2.GetAgentVersionRequest(
            agent_id=self.agent_id,
            version_id=self.version_id,
            user_id=self.user_id
        )

        # Mock database queries
        agent_query = MagicMock()
        agent_query.filter.return_value.first.return_value = self.agent_mock

        version_query = MagicMock()
        version_query.filter.return_value.first.return_value = self.version_mock

        self.db_mock.query.side_effect = [agent_query, version_query]

        # Execute
        with patch('app.services.agent_version_functions._agent_version_to_protobuf') as mock_protobuf:
            # Create a real protobuf object instead of MagicMock
            mock_version_proto = agent_pb2.AgentVersion(
                id=self.version_id,
                version_number="1.0.0",
                is_current=True
            )
            mock_protobuf.return_value = mock_version_proto

            response = self.agent_version_functions.getAgentVersion(request, self.context_mock)

        # Assert
        self.assertTrue(response.success)
        self.assertIn("Retrieved version 1.0.0", response.message)
        self.assertEqual(response.version.id, mock_version_proto.id)
        self.assertEqual(response.version.version_number, mock_version_proto.version_number)
        mock_protobuf.assert_called_once_with(self.version_mock, True)

    def test_get_agent_version_not_found(self):
        """Test getting non-existent version"""
        # Setup
        request = agent_pb2.GetAgentVersionRequest(
            agent_id=self.agent_id,
            version_id="non-existent-version",
            user_id=self.user_id
        )
        
        # Mock database queries
        agent_query = MagicMock()
        agent_query.filter.return_value.first.return_value = self.agent_mock
        
        version_query = MagicMock()
        version_query.filter.return_value.first.return_value = None
        
        self.db_mock.query.side_effect = [agent_query, version_query]
        
        # Execute
        response = self.agent_version_functions.getAgentVersion(request, self.context_mock)
        
        # Assert
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Version not found")
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    def test_switch_agent_version_success(self):
        """Test switching agent version successfully"""
        # Setup
        request = agent_pb2.SwitchAgentVersionRequest(
            agent_id=self.agent_id,
            version_id="different-version-id",  # Use different version ID
            user_id=self.user_id
        )

        # Mock database queries
        agent_query = MagicMock()
        agent_query.filter.return_value.first.return_value = self.agent_mock

        version_query = MagicMock()
        version_query.filter.return_value.first.return_value = self.version_mock

        self.db_mock.query.side_effect = [agent_query, version_query]

        # Execute
        with patch('app.services.agent_version_functions._agent_version_to_protobuf') as mock_protobuf:
            # Create a real protobuf object instead of MagicMock
            mock_version_proto = agent_pb2.AgentVersion(
                id=self.version_id,
                version_number="1.0.0",
                is_current=True
            )
            mock_protobuf.return_value = mock_version_proto

            response = self.agent_version_functions.switchAgentVersion(request, self.context_mock)

        # Assert
        self.assertTrue(response.success)
        self.assertIn("Successfully switched to version 1.0.0", response.message)
        self.assertEqual(response.new_current_version.id, mock_version_proto.id)
        self.assertEqual(response.new_current_version.version_number, mock_version_proto.version_number)
        self.db_mock.commit.assert_called()

    def test_switch_agent_version_already_current(self):
        """Test switching to version that is already current"""
        # Setup
        request = agent_pb2.SwitchAgentVersionRequest(
            agent_id=self.agent_id,
            version_id=self.version_id,  # Same as current version
            user_id=self.user_id
        )

        # Mock database queries
        agent_query = MagicMock()
        agent_query.filter.return_value.first.return_value = self.agent_mock

        version_query = MagicMock()
        version_query.filter.return_value.first.return_value = self.version_mock

        self.db_mock.query.side_effect = [agent_query, version_query]

        # Execute
        with patch('app.services.agent_version_functions._agent_version_to_protobuf') as mock_protobuf:
            # Create a real protobuf object instead of MagicMock
            mock_version_proto = agent_pb2.AgentVersion(
                id=self.version_id,
                version_number="1.0.0",
                is_current=True
            )
            mock_protobuf.return_value = mock_version_proto

            response = self.agent_version_functions.switchAgentVersion(request, self.context_mock)

        # Assert
        self.assertTrue(response.success)
        self.assertIn("is already the current version", response.message)
        self.assertEqual(response.new_current_version.id, mock_version_proto.id)
        self.assertEqual(response.new_current_version.version_number, mock_version_proto.version_number)

    def test_switch_agent_version_permission_denied(self):
        """Test switching version without permission"""
        # Setup
        request = agent_pb2.SwitchAgentVersionRequest(
            agent_id=self.agent_id,
            version_id=self.version_id,
            user_id=self.other_user_id  # Different user
        )
        
        # Mock database queries
        agent_query = MagicMock()
        agent_query.filter.return_value.first.return_value = self.agent_mock
        
        self.db_mock.query.side_effect = [agent_query]
        
        # Execute
        response = self.agent_version_functions.switchAgentVersion(request, self.context_mock)
        
        # Assert
        self.assertFalse(response.success)
        self.assertIn("Permission denied", response.message)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    def test_create_version_and_publish_success(self):
        """Test creating version and publishing successfully"""
        # Setup
        request = agent_pb2.CreateVersionAndPublishRequest(
            agent_id=self.agent_id,
            user_id=self.user_id,
            publish_to_marketplace=True
        )

        # Agent has updates and is public
        self.agent_mock.is_updated = True
        self.agent_mock.visibility = AgentVisibilityEnum.PUBLIC
        
        # Execute
        with patch('app.services.agent_version_functions.SessionLocal') as mock_session_local, \
             patch('app.services.agent_version_functions._create_new_version_from_agent') as mock_create_version, \
             patch('app.services.agent_version_functions._create_marketplace_listing_from_agent') as mock_create_listing:
            
            # Create a new mock db instance for this method
            mock_db = MagicMock()
            mock_session_local.return_value = mock_db
            
            # Mock database queries on the new db instance
            mock_db.query.return_value.filter.return_value.first.return_value = self.agent_mock
            
            # Create a proper mock version with all required attributes
            mock_new_version = MagicMock()
            mock_new_version.id = "new-version-id"
            mock_new_version.version_number = "1.1.0"
            mock_create_version.return_value = mock_new_version
            
            mock_listing = MagicMock()
            mock_listing.id = "listing-123"
            mock_create_listing.return_value = mock_listing
            
            response = self.agent_version_functions.createVersionAndPublish(request, self.context_mock)
        
        # Assert
        self.assertTrue(response.success)
        self.assertTrue(response.version_created)
        self.assertTrue(response.marketplace_updated)
        self.assertEqual(response.version_number, "1.1.0")
        self.assertEqual(response.version_id, "new-version-id")
        self.assertEqual(response.marketplace_listing_id, "listing-123")
        self.assertIn("Successfully created version 1.1.0", response.message)
        self.assertIn("updated marketplace listing", response.message)

    def test_create_version_and_publish_no_changes(self):
        """Test creating version when no changes exist"""
        # Setup
        request = agent_pb2.CreateVersionAndPublishRequest(
            agent_id=self.agent_id,
            user_id=self.user_id,
            publish_to_marketplace=False
        )
        
        # Agent has no updates
        self.agent_mock.is_updated = False
        
        # Execute
        with patch('app.services.agent_version_functions.SessionLocal') as mock_session_local:
            # Create a new mock db instance for this method
            mock_db = MagicMock()
            mock_session_local.return_value = mock_db
            
            # Mock database queries on the new db instance
            mock_db.query.return_value.filter.return_value.first.return_value = self.agent_mock
            
            response = self.agent_version_functions.createVersionAndPublish(request, self.context_mock)
        
        # Assert
        self.assertTrue(response.success)
        self.assertFalse(response.version_created)
        self.assertFalse(response.marketplace_updated)
        self.assertIn("No pending changes", response.message)

    def test_create_version_and_publish_permission_denied(self):
        """Test creating version without permission"""
        # Setup
        request = agent_pb2.CreateVersionAndPublishRequest(
            agent_id=self.agent_id,
            user_id=self.other_user_id,  # Different user
            publish_to_marketplace=False
        )
        
        # Execute
        with patch('app.services.agent_version_functions.SessionLocal') as mock_session_local:
            # Create a new mock db instance for this method
            mock_db = MagicMock()
            mock_session_local.return_value = mock_db
            
            # Mock database queries on the new db instance
            mock_db.query.return_value.filter.return_value.first.return_value = self.agent_mock
            
            response = self.agent_version_functions.createVersionAndPublish(request, self.context_mock)
        
        # Assert
        self.assertFalse(response.success)
        self.assertFalse(response.version_created)
        self.assertFalse(response.marketplace_updated)
        self.assertIn("Permission denied", response.message)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)


if __name__ == "__main__":
    unittest.main()