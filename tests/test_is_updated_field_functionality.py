"""
Test the is_updated field functionality implementation in agent-service.

This test verifies:
1. New agents have is_updated=False by default
2. When agent is updated, is_updated=True is set
3. getAgent returns is_updated field
4. createVersionAndPublish API resets is_updated=False
5. Derived agents get is_updated=True when template changes
"""

import sys
import os
import json
from datetime import datetime
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

try:
    from app.services.agent_functions import AgentFunctionsService
    from app.services.agent_version_functions import AgentVersionFunctions
    from app.grpc import agent_pb2
    from app.models.agent import AgentConfig, AgentConfigVersion
    from app.db.session import SessionLocal
    from app.utils.constants.constants import AgentStatusEnum, AgentVisibilityEnum, AgentOwnerTypeEnum, AgentCategoryEnum
    from unittest.mock import Mock, patch
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import failed: {str(e)}")
    traceback.print_exc()
    sys.exit(1)


def test_new_agent_has_is_updated_false():
    """Test that new agents have is_updated=False by default"""
    
    print("=== Test: New Agent has is_updated=False ===")
    
    agent_functions = AgentFunctionsService()
    db = SessionLocal()
    
    try:
        # Create a new agent
        test_agent = AgentConfig(
            id="test-new-agent-123",
            name="New Test Agent",
            description="Test agent for is_updated field",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,  # Should be False by default
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
            tags=["test"]
        )
        
        db.add(test_agent)
        db.commit()
        
        # Test getAgent
        get_request = agent_pb2.GetAgentRequest()
        get_request.id = test_agent.id
        get_request.user_id = "test-user-123"
        
        context = Mock()
        
        # Patch the module-level get_db function, not the class method
        with patch('app.services.agent_functions.get_db', return_value=db):
            response = agent_functions.getAgent(get_request, context)
        
        print(f"Get agent response: {response.success} - {response.message}")
        
        assert response.success, f"getAgent failed: {response.message}"
        
        # Check if agent has is_updated field and it's False
        agent_proto = response.agent
        assert hasattr(agent_proto, 'is_updated'), "Agent protobuf doesn't have is_updated field"
        assert agent_proto.is_updated == False, f"Expected is_updated=False, got {agent_proto.is_updated}"
        
        print("✅ New agent has is_updated=False by default")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            db.query(AgentConfig).filter(AgentConfig.id == "test-new-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_agent_update_sets_is_updated_true():
    """Test that updating an agent sets is_updated=True"""
    
    print("=== Test: Agent Update sets is_updated=True ===")
    
    agent_functions = AgentFunctionsService()
    db = SessionLocal()
    
    try:
        # Create an agent with is_updated=False
        test_agent = AgentConfig(
            id="test-update-agent-123",
            name="Test Update Agent",
            description="Original description",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,  # Start with False
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Original system message",
            tags=["test"]
        )
        
        db.add(test_agent)
        db.commit()
        
        print(f"✅ Created agent with is_updated=False")
        
        # Update the agent core details
        update_request = agent_pb2.UpdateAgentCoreDetailsRequest()
        update_request.agent_id = test_agent.id
        update_request.name = "Updated Test Agent"
        update_request.description = "Updated description"
        update_request.instructions = "Updated instructions"
        update_request.owner.id = "test-user-123"
        
        context = Mock()
        
        # Patch the module-level get_db function, not the class method
        with patch('app.services.agent_functions.get_db', return_value=db):
            response = agent_functions.UpdateAgentCoreDetails(update_request, context)
        
        print(f"Update response: {response.success} - {response.message}")
        
        assert response.success, f"Update failed: {response.message}"
        
        # Check if is_updated was set to True
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        
        assert hasattr(updated_agent, 'is_updated'), "Agent model doesn't have is_updated field"
        assert updated_agent.is_updated == True, f"Expected is_updated=True after update, got {updated_agent.is_updated}"
        
        print("✅ Agent update sets is_updated=True")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            db.query(AgentConfig).filter(AgentConfig.id == "test-update-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_knowledge_update_sets_is_updated_true():
    """Test that updating agent knowledge sets is_updated=True"""
    
    print("=== Test: Knowledge Update sets is_updated=True ===")
    
    agent_functions = AgentFunctionsService()
    db = SessionLocal()
    
    try:
        # Create an agent with is_updated=False
        test_agent = AgentConfig(
            id="test-knowledge-agent-123",
            name="Test Knowledge Agent",
            description="Test agent for knowledge updates",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,  # Start with False
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
            tags=["test"]
        )
        
        db.add(test_agent)
        db.commit()
        
        print(f"✅ Created agent with is_updated=False")
        
        # Update the agent knowledge
        update_request = agent_pb2.UpdateAgentKnowledgeRequest()
        update_request.agent_id = test_agent.id
        update_request.knowledge.append("New knowledge item")
        update_request.owner.id = "test-user-123"
        
        context = Mock()
        
        # Patch the module-level get_db function, not the class method
        with patch('app.services.agent_functions.get_db', return_value=db):
            response = agent_functions.UpdateAgentKnowledge(update_request, context)
        
        print(f"Knowledge update response: {response.success} - {response.message}")
        
        assert response.success, f"Knowledge update failed: {response.message}"
        
        # Check if is_updated was set to True
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        
        assert updated_agent.is_updated == True, f"Expected is_updated=True after knowledge update, got {updated_agent.is_updated}"
        
        print("✅ Knowledge update sets is_updated=True")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            db.query(AgentConfig).filter(AgentConfig.id == "test-knowledge-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_create_version_and_publish_resets_is_updated():
    """Test that createVersionAndPublish resets is_updated=False"""
    
    print("=== Test: Create Version and Publish resets is_updated=False ===")
    
    agent_version_functions = AgentVersionFunctions()
    db = SessionLocal()
    
    try:
        # Create an agent with is_updated=True
        test_agent = AgentConfig(
            id="test-version-agent-123",
            name="Test Version Agent",
            description="Test agent for version creation",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=True,  # Has pending changes
            visibility=AgentVisibilityEnum.PUBLIC,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
            tags=["test"]
        )
        
        db.add(test_agent)
        db.flush()
        
        # Create initial version
        initial_version = AgentConfigVersion(
            agent_config_id=test_agent.id,
            version_number="1.0.0",
            name=test_agent.name,
            description=test_agent.description,
            avatar=test_agent.avatar,
            agent_category=test_agent.agent_category,
            system_message=test_agent.system_message,
            tags=test_agent.tags,
            version_notes="Initial version"
        )
        
        db.add(initial_version)
        db.flush()
        
        test_agent.current_version_id = initial_version.id
        db.commit()
        
        # Store the agent ID before it gets detached
        agent_id = test_agent.id
        
        print(f"✅ Created agent with is_updated=True")
        
        # Test the createVersionAndPublish functionality
        version_request = agent_pb2.CreateVersionAndPublishRequest()
        version_request.agent_id = agent_id
        version_request.user_id = "test-user-123"
        version_request.publish_to_marketplace = True
        
        context = Mock()
        
        # Patch the module-level get_db function, not the class method
        with patch('app.services.agent_version_functions.get_db', return_value=db):
            with patch('app.services.agent_version_functions.SessionLocal', return_value=db):
                version_response = agent_version_functions.createVersionAndPublish(version_request, context)
        
        print(f"Create version response: {version_response.success} - {version_response.message}")
        
        assert version_response.success, f"Create version failed: {version_response.message}"
        
        # After creating version, is_updated should be reset to False
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == agent_id).first()
        
        assert updated_agent.is_updated == False, f"Expected is_updated=False after version creation, got {updated_agent.is_updated}"
        
        print("✅ Create version and publish resets is_updated=False")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            # Set current_version_id to NULL first
            agent = db.query(AgentConfig).filter(AgentConfig.id == "test-version-agent-123").first()
            if agent:
                agent.current_version_id = None
                db.commit()
            
            # Delete versions
            db.query(AgentConfigVersion).filter(AgentConfigVersion.agent_config_id == "test-version-agent-123").delete()
            db.commit()
            
            # Delete agent
            db.query(AgentConfig).filter(AgentConfig.id == "test-version-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_no_pending_changes_version_creation():
    """Test that version creation fails when no pending changes (is_updated=False)"""
    
    print("=== Test: No pending changes prevents version creation ===")
    
    agent_version_functions = AgentVersionFunctions()
    db = SessionLocal()
    
    try:
        # Create an agent with is_updated=False (no pending changes)
        test_agent = AgentConfig(
            id="test-no-changes-agent-123",
            name="Test No Changes Agent",
            description="Test agent with no pending changes",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,  # No pending changes
            visibility=AgentVisibilityEnum.PUBLIC,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
            tags=["test"]
        )
        
        db.add(test_agent)
        db.flush()
        
        # Create initial version
        initial_version = AgentConfigVersion(
            agent_config_id=test_agent.id,
            version_number="1.0.0",
            name=test_agent.name,
            description=test_agent.description,
            # Remove agent_url and builder_url as they don't exist in AgentConfigVersion
            # core_details is also not a field in AgentConfigVersion
            category=test_agent.category,
            tags=test_agent.tags,
            changelog="Initial version"
        )
        
        db.add(initial_version)
        db.flush()
        
        test_agent.current_version_id = initial_version.id
        db.commit()
        
        # Store the agent ID before it gets detached
        agent_id = test_agent.id
        
        print(f"✅ Created agent with is_updated=False")
        
        # Test the createVersionAndPublish functionality
        version_request = agent_pb2.CreateVersionAndPublishRequest()
        version_request.agent_id = agent_id
        version_request.user_id = "test-user-123"
        version_request.publish_to_marketplace = True
        
        context = Mock()
        
        # Patch the module-level get_db function, not the class method
        with patch('app.services.agent_version_functions.get_db', return_value=db):
            with patch('app.services.agent_version_functions.SessionLocal', return_value=db):
                version_response = agent_version_functions.createVersionAndPublish(version_request, context)
        
        print(f"Create version response: {version_response.success} - {version_response.message}")
        
        # Should succeed but indicate no version was created
        assert version_response.success, f"Create version should succeed but indicate no changes: {version_response.message}"
        assert not version_response.version_created, "Expected version_created=False when no pending changes"
        assert "No pending changes" in version_response.message or "up to date" in version_response.message, "Expected message about no pending changes"
        
        print("✅ No pending changes prevents version creation")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            # Set current_version_id to NULL first
            agent = db.query(AgentConfig).filter(AgentConfig.id == "test-no-changes-agent-123").first()
            if agent:
                agent.current_version_id = None
                db.commit()
            
            # Delete versions
            db.query(AgentConfigVersion).filter(AgentConfigVersion.agent_config_id == "test-no-changes-agent-123").delete()
            db.commit()
            
            # Delete agent
            db.query(AgentConfig).filter(AgentConfig.id == "test-no-changes-agent-123").delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def test_derived_agent_update_notification():
    """Test that derived agents get is_updated=True when template changes"""
    
    print("=== Test: Derived agents get update notification ===")
    
    agent_functions = AgentFunctionsService()
    db = SessionLocal()
    
    try:
        # Create a template agent
        template_agent = AgentConfig(
            id="test-template-agent-123",
            name="Template Agent",
            description="Template agent",
            avatar="https://example.com/avatar.png",
            owner_id="template-owner-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,
            visibility=AgentVisibilityEnum.PUBLIC,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Template system message",
            tags=["template"]
        )
        
        # Create a derived agent
        derived_agent = AgentConfig(
            id="test-derived-agent-123",
            name="Derived Agent",
            description="Derived from template",
            avatar="https://example.com/avatar.png",
            owner_id="derived-owner-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Derived system message",
            tags=["derived"],
            # Link to template
            agent_template_id=template_agent.id,
            template_owner_id=template_agent.owner_id,
            is_imported=True
        )
        
        db.add(template_agent)
        db.add(derived_agent)
        db.commit()
        
        print(f"✅ Created template and derived agents with is_updated=False")
        
        # Update the template agent
        update_request = agent_pb2.UpdateAgentCoreDetailsRequest()
        update_request.agent_id = template_agent.id
        update_request.name = "Updated Template Agent"
        update_request.description = "Updated template description"
        update_request.instructions = "Updated template instructions"
        update_request.owner.id = "template-owner-123"
        
        context = Mock()
        
        # Patch the module-level get_db function, not the class method
        with patch('app.services.agent_functions.get_db', return_value=db):
            response = agent_functions.UpdateAgentCoreDetails(update_request, context)
        
        print(f"Template update response: {response.success} - {response.message}")
        
        assert response.success, f"Template update failed: {response.message}"
        
        # Check if derived agent got is_updated=True
        updated_derived_agent = db.query(AgentConfig).filter(AgentConfig.id == derived_agent.id).first()
        
        assert updated_derived_agent.is_updated == True, f"Expected derived agent is_updated=True, got {updated_derived_agent.is_updated}"
        
        print("✅ Derived agents get update notification when template changes")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        raise
        
    finally:
        # Cleanup
        try:
            db.query(AgentConfig).filter(AgentConfig.id.in_(["test-template-agent-123", "test-derived-agent-123"])).delete()
            db.commit()
            print("✅ Cleanup completed")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup warning: {cleanup_error}")
            db.rollback()
        finally:
            db.close()


def run_all_tests():
    """Run all is_updated field functionality tests"""
    
    print("🧪 Running is_updated field functionality tests for agent-service...")
    
    tests = [
        test_new_agent_has_is_updated_false,
        test_agent_update_sets_is_updated_true,
        test_knowledge_update_sets_is_updated_true,
        test_create_version_and_publish_resets_is_updated,
        test_no_pending_changes_version_creation,
        test_derived_agent_update_notification
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
            print(f"✅ {test.__name__} PASSED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED with exception: {str(e)}\n")
    
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)