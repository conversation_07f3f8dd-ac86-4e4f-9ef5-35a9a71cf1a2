#!/usr/bin/env python3
"""
Test script to verify agent version routes implementation.
"""

import asyncio
import sys
import os

# Add the api-gateway directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'api-gateway'))

from app.services.agent_service import AgentServiceClient

async def test_agent_service_methods():
    """Test that all agent version methods are properly implemented."""
    
    print("🧪 Testing Agent Service Client Methods...")
    
    # Initialize the client
    client = AgentServiceClient()
    
    # Test that all required methods exist
    required_methods = [
        'list_agent_versions',
        'get_agent_version', 
        'switch_agent_version',
        'create_version_and_publish',
        'get_agent'
    ]
    
    print("\n✅ Checking method availability:")
    for method_name in required_methods:
        if hasattr(client, method_name):
            method = getattr(client, method_name)
            if callable(method):
                print(f"  ✓ {method_name} - Available")
            else:
                print(f"  ✗ {method_name} - Not callable")
                return False
        else:
            print(f"  ✗ {method_name} - Missing")
            return False
    
    print("\n✅ All required methods are available!")
    
    # Test method signatures
    print("\n✅ Checking method signatures:")
    
    # Check list_agent_versions signature
    import inspect
    sig = inspect.signature(client.list_agent_versions)
    expected_params = ['agent_id', 'user_id', 'page', 'page_size']
    actual_params = list(sig.parameters.keys())[1:]  # Skip 'self'
    if actual_params == expected_params:
        print(f"  ✓ list_agent_versions signature correct: {actual_params}")
    else:
        print(f"  ✗ list_agent_versions signature incorrect. Expected: {expected_params}, Got: {actual_params}")
        return False
    
    # Check get_agent_version signature
    sig = inspect.signature(client.get_agent_version)
    expected_params = ['agent_id', 'version_id', 'user_id']
    actual_params = list(sig.parameters.keys())[1:]  # Skip 'self'
    if actual_params == expected_params:
        print(f"  ✓ get_agent_version signature correct: {actual_params}")
    else:
        print(f"  ✗ get_agent_version signature incorrect. Expected: {expected_params}, Got: {actual_params}")
        return False
    
    # Check switch_agent_version signature
    sig = inspect.signature(client.switch_agent_version)
    expected_params = ['agent_id', 'version_id', 'user_id']
    actual_params = list(sig.parameters.keys())[1:]  # Skip 'self'
    if actual_params == expected_params:
        print(f"  ✓ switch_agent_version signature correct: {actual_params}")
    else:
        print(f"  ✗ switch_agent_version signature incorrect. Expected: {expected_params}, Got: {actual_params}")
        return False
    
    # Check create_version_and_publish signature
    sig = inspect.signature(client.create_version_and_publish)
    expected_params = ['agent_id', 'user_id', 'publish_to_marketplace']
    actual_params = list(sig.parameters.keys())[1:]  # Skip 'self'
    if actual_params == expected_params:
        print(f"  ✓ create_version_and_publish signature correct: {actual_params}")
    else:
        print(f"  ✗ create_version_and_publish signature incorrect. Expected: {expected_params}, Got: {actual_params}")
        return False
    
    # Check get_agent signature
    sig = inspect.signature(client.get_agent)
    expected_params = ['agent_id', 'user_id']
    actual_params = list(sig.parameters.keys())[1:]  # Skip 'self'
    if actual_params == expected_params:
        print(f"  ✓ get_agent signature correct: {actual_params}")
    else:
        print(f"  ✗ get_agent signature incorrect. Expected: {expected_params}, Got: {actual_params}")
        return False
    
    print("\n✅ All method signatures are correct!")
    
    return True

def test_route_imports():
    """Test that the route files can be imported without errors."""
    
    print("\n🧪 Testing Route Imports...")
    
    try:
        # Test agent routes import
        from app.api.routers.agent_routes import router as agent_router
        print("  ✓ Agent routes imported successfully")
        
        # Test marketplace routes import  
        from app.api.routers.marketplace_routes import router as marketplace_router
        print("  ✓ Marketplace routes imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"  ✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")
        return False

async def main():
    """Main test function."""
    
    print("🚀 Agent Version Routes Test Suite")
    print("=" * 50)
    
    # Test 1: Agent Service Methods
    service_test_passed = await test_agent_service_methods()
    
    # Test 2: Route Imports
    import_test_passed = test_route_imports()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  Agent Service Methods: {'✅ PASS' if service_test_passed else '❌ FAIL'}")
    print(f"  Route Imports: {'✅ PASS' if import_test_passed else '❌ FAIL'}")
    
    if service_test_passed and import_test_passed:
        print("\n🎉 All tests passed! Agent version routes are ready.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)