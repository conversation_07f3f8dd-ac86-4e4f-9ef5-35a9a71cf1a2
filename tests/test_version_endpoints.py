#!/usr/bin/env python3
"""
Simple test script to verify agent version management endpoints are working
without requiring Redis connection.
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_imports():
    """Test that all required modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        # Test protobuf imports
        from app.grpc_ import agent_pb2, agent_pb2_grpc
        print("✓ Protobuf imports successful")
        
        # Test service client import
        from app.services.agent_service import AgentServiceClient
        print("✓ AgentServiceClient import successful")
        
        # Test schema imports
        from app.schemas.agent import (
            AgentVersionInDB, 
            ListAgentVersionsResponse,
            GetAgentVersionResponse,
            SwitchAgentVersionResponse,
            CreateAgentVersionResponse
        )
        print("✓ Agent version schemas import successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_protobuf_messages():
    """Test that protobuf messages are available."""
    print("\nTesting protobuf messages...")
    
    try:
        from app.grpc_ import agent_pb2
        
        # Check if version management messages exist
        messages = [
            'ListAgentVersionsRequest',
            'ListAgentVersionsResponse', 
            'GetAgentVersionRequest',
            'GetAgentVersionResponse',
            'SwitchAgentVersionRequest',
            'SwitchAgentVersionResponse',
            'CreateVersionAndPublishRequest',
            'CreateVersionAndPublishResponse',
            'AgentVersion',
            'AgentModelConfig',
            'AgentKnowledgeBase',
            'MarketplaceListing'
        ]
        
        for msg in messages:
            if hasattr(agent_pb2, msg):
                print(f"✓ {msg} available")
            else:
                print(f"✗ {msg} missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"✗ Protobuf test failed: {e}")
        return False

def test_service_methods():
    """Test that service client has the required methods."""
    print("\nTesting service client methods...")
    
    try:
        from app.services.agent_service import AgentServiceClient
        
        # Check if version management methods exist
        methods = [
            'list_agent_versions',
            'get_agent_version', 
            'switch_agent_version',
            'create_version_and_publish'
        ]
        
        client = AgentServiceClient()
        
        for method in methods:
            if hasattr(client, method):
                print(f"✓ {method} available")
            else:
                print(f"✗ {method} missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"✗ Service client test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Agent Version Management Implementation Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_protobuf_messages,
        test_service_methods
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 50)
    if all(results):
        print("🎉 All tests passed! Agent version management is properly implemented.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())