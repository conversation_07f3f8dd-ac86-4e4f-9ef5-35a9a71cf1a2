import unittest
from unittest.mock import MagicMock, patch
import grpc
from datetime import datetime, timezone
from app.grpc import agent_pb2
from app.services.agent_functions import AgentFunctionsService
from app.services.marketplace_functions import AgentMarketplaceFunctionsService
from app.models.agent import AgentConfig
# AgentTemplate model doesn't exist - template functionality not implemented
from app.models.agent_rating import AgentRating


class TestRatingAndUsage(unittest.TestCase):
    def setUp(self):
        self.agent_functions = AgentFunctionsService()
        self.marketplace_functions = AgentMarketplaceFunctionsService()

        # Mock the database session
        self.db_mock = MagicMock()
        self.agent_functions.get_db = MagicMock(return_value=self.db_mock)
        self.marketplace_functions.get_db = MagicMock(return_value=self.db_mock)

        # Mock gRPC context
        self.context_mock = MagicMock()

        # Test data
        self.agent_id = "test-agent-id"
        self.template_id = "test-template-id"
        self.user_id = "test-user-id"
        self.rating = 4.5

        # Mock agent and template
        self.agent_mock = MagicMock(spec=AgentConfig)
        self.agent_mock.id = self.agent_id
        self.agent_mock.name = "Test Agent"
        self.agent_mock.template_id = self.template_id
        self.agent_mock.average_rating = 0.0

        self.template_mock = MagicMock()  # AgentTemplate not implemented yet
        self.template_mock.id = self.template_id
        self.template_mock.name = "Test Template"
        self.template_mock.owner_id = "template-owner-id"
        self.template_mock.use_count = 0
        self.template_mock.average_rating = 0.0

        # Mock rating
        self.rating_mock = MagicMock(spec=AgentRating)
        self.rating_mock.agent_id = self.agent_id
        self.rating_mock.user_id = self.user_id
        self.rating_mock.rating = self.rating

    def test_rate_agent_new_rating(self):
        # Setup
        request = agent_pb2.RateAgentRequest(
            agent_id=self.agent_id, user_id=self.user_id, rating=self.rating
        )

        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.side_effect = [
            self.agent_mock,  # First call returns the agent
            None,  # Second call returns no existing rating
        ]
        self.db_mock.query.return_value.filter.return_value.all.return_value = [self.rating_mock]

        # Execute
        response = self.agent_functions.rateAgent(request, self.context_mock)

        # Assert
        self.assertTrue(response.success)
        self.assertEqual(response.average_rating, self.rating)
        self.db_mock.add.assert_called_once()
        self.db_mock.commit.assert_called()

    def test_rate_agent_update_existing(self):
        # Setup
        request = agent_pb2.RateAgentRequest(
            agent_id=self.agent_id, user_id=self.user_id, rating=self.rating
        )

        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.side_effect = [
            self.agent_mock,  # First call returns the agent
            self.rating_mock,  # Second call returns existing rating
        ]
        self.db_mock.query.return_value.filter.return_value.all.return_value = [self.rating_mock]

        # Execute
        response = self.agent_functions.rateAgent(request, self.context_mock)

        # Assert
        self.assertTrue(response.success)
        self.assertEqual(response.average_rating, self.rating)
        self.db_mock.add.assert_not_called()
        self.db_mock.commit.assert_called()

    def test_rate_agent_invalid_rating(self):
        # Setup
        request = agent_pb2.RateAgentRequest(
            agent_id=self.agent_id, user_id=self.user_id, rating=6.0  # Invalid rating (> 5.0)
        )

        # Execute
        response = self.agent_functions.rateAgent(request, self.context_mock)

        # Assert
        self.assertFalse(response.success)
        self.assertEqual(response.average_rating, 0.0)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.INVALID_ARGUMENT)

    def test_use_agent(self):
        # Setup
        request = agent_pb2.UseAgentRequest(agent_id=self.template_id, user_id=self.user_id)

        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.template_mock

        # Execute
        response = self.marketplace_functions.useAgent(request, self.context_mock)

        # Assert - Template functionality not implemented yet
        self.assertFalse(response.success)
        self.assertEqual(response.use_count, 0)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.UNIMPLEMENTED)

    def test_use_agent_not_found(self):
        # Setup
        request = agent_pb2.UseAgentRequest(agent_id="non-existent-id", user_id=self.user_id)

        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = None

        # Execute
        response = self.marketplace_functions.useAgent(request, self.context_mock)

        # Assert - Template functionality not implemented yet
        self.assertFalse(response.success)
        self.assertEqual(response.use_count, 0)
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.UNIMPLEMENTED)


if __name__ == "__main__":
    unittest.main()
