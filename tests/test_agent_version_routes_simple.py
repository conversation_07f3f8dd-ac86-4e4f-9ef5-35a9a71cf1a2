#!/usr/bin/env python3
"""
Simple test script to verify agent version routes implementation without dependencies.
"""

import ast
import os
import sys

def check_agent_service_methods():
    """Check that all required methods exist in the agent service file."""
    
    print("🧪 Testing Agent Service Client Methods...")
    
    # Read the agent service file
    agent_service_path = os.path.join(os.path.dirname(__file__), '..', 'api-gateway', 'app', 'services', 'agent_service.py')
    
    if not os.path.exists(agent_service_path):
        print(f"  ✗ Agent service file not found: {agent_service_path}")
        return False
    
    with open(agent_service_path, 'r') as f:
        content = f.read()
    
    # Parse the AST to find method definitions
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"  ✗ Syntax error in agent service file: {e}")
        return False
    
    # Find the AgentServiceClient class
    agent_service_class = None
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef) and node.name == 'AgentServiceClient':
            agent_service_class = node
            break
    
    if not agent_service_class:
        print("  ✗ AgentServiceClient class not found")
        return False
    
    # Extract method names
    method_names = []
    for node in agent_service_class.body:
        if isinstance(node, ast.AsyncFunctionDef):
            method_names.append(node.name)
    
    # Check required methods
    required_methods = [
        'list_agent_versions',
        'get_agent_version', 
        'switch_agent_version',
        'create_version_and_publish',
        'get_agent'
    ]
    
    print("\n✅ Checking method availability:")
    all_methods_found = True
    for method_name in required_methods:
        if method_name in method_names:
            print(f"  ✓ {method_name} - Available")
        else:
            print(f"  ✗ {method_name} - Missing")
            all_methods_found = False
    
    if all_methods_found:
        print("\n✅ All required methods are available!")
    
    return all_methods_found

def check_agent_routes():
    """Check that agent routes file has the required endpoints."""
    
    print("\n🧪 Testing Agent Routes...")
    
    # Read the agent routes file
    agent_routes_path = os.path.join(os.path.dirname(__file__), '..', 'api-gateway', 'app', 'api', 'routers', 'agent_routes.py')
    
    if not os.path.exists(agent_routes_path):
        print(f"  ✗ Agent routes file not found: {agent_routes_path}")
        return False
    
    with open(agent_routes_path, 'r') as f:
        content = f.read()
    
    # Check for required route patterns
    required_patterns = [
        'list_agent_versions',
        'get_agent_version',
        'switch_agent_version',
        'create_version_and_publish',
        'list_public_agent_versions',
        'get_public_agent_version'
    ]
    
    print("\n✅ Checking route endpoints:")
    all_routes_found = True
    for pattern in required_patterns:
        if pattern in content:
            print(f"  ✓ {pattern} - Found")
        else:
            print(f"  ✗ {pattern} - Missing")
            all_routes_found = False
    
    if all_routes_found:
        print("\n✅ All required route endpoints are available!")
    
    return all_routes_found

def check_marketplace_routes():
    """Check that marketplace routes file has the required public endpoints."""
    
    print("\n🧪 Testing Marketplace Routes...")
    
    # Read the marketplace routes file
    marketplace_routes_path = os.path.join(os.path.dirname(__file__), '..', 'api-gateway', 'app', 'api', 'routers', 'marketplace_routes.py')
    
    if not os.path.exists(marketplace_routes_path):
        print(f"  ✗ Marketplace routes file not found: {marketplace_routes_path}")
        return False
    
    with open(marketplace_routes_path, 'r') as f:
        content = f.read()
    
    # Check for required public route patterns
    required_patterns = [
        'list_public_agent_versions',
        'get_public_agent_version'
    ]
    
    print("\n✅ Checking public route endpoints:")
    all_routes_found = True
    for pattern in required_patterns:
        if pattern in content:
            print(f"  ✓ {pattern} - Found")
        else:
            print(f"  ✗ {pattern} - Missing")
            all_routes_found = False
    
    if all_routes_found:
        print("\n✅ All required public route endpoints are available!")
    
    return all_routes_found

def check_proto_definitions():
    """Check that proto definitions have the required messages."""
    
    print("\n🧪 Testing Proto Definitions...")
    
    # Read the proto file
    proto_path = os.path.join(os.path.dirname(__file__), 'proto-definitions', 'agent.proto')
    
    if not os.path.exists(proto_path):
        print(f"  ✗ Proto file not found: {proto_path}")
        return False
    
    with open(proto_path, 'r') as f:
        content = f.read()
    
    # Check for required message types
    required_messages = [
        'ListAgentVersionsRequest',
        'ListAgentVersionsResponse',
        'GetAgentVersionRequest',
        'GetAgentVersionResponse',
        'SwitchAgentVersionRequest',
        'SwitchAgentVersionResponse',
        'CreateVersionAndPublishRequest',
        'CreateVersionAndPublishResponse',
        'AgentVersion'
    ]
    
    print("\n✅ Checking proto message definitions:")
    all_messages_found = True
    for message in required_messages:
        if f"message {message}" in content:
            print(f"  ✓ {message} - Found")
        else:
            print(f"  ✗ {message} - Missing")
            all_messages_found = False
    
    if all_messages_found:
        print("\n✅ All required proto messages are available!")
    
    return all_messages_found

def main():
    """Main test function."""
    
    print("🚀 Agent Version Routes Test Suite (Simple)")
    print("=" * 60)
    
    # Test 1: Agent Service Methods
    service_test_passed = check_agent_service_methods()
    
    # Test 2: Agent Routes
    agent_routes_test_passed = check_agent_routes()
    
    # Test 3: Marketplace Routes
    marketplace_routes_test_passed = check_marketplace_routes()
    
    # Test 4: Proto Definitions
    proto_test_passed = check_proto_definitions()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"  Agent Service Methods: {'✅ PASS' if service_test_passed else '❌ FAIL'}")
    print(f"  Agent Routes: {'✅ PASS' if agent_routes_test_passed else '❌ FAIL'}")
    print(f"  Marketplace Routes: {'✅ PASS' if marketplace_routes_test_passed else '❌ FAIL'}")
    print(f"  Proto Definitions: {'✅ PASS' if proto_test_passed else '❌ FAIL'}")
    
    all_tests_passed = all([
        service_test_passed,
        agent_routes_test_passed,
        marketplace_routes_test_passed,
        proto_test_passed
    ])
    
    if all_tests_passed:
        print("\n🎉 All tests passed! Agent version routes are ready.")
        print("\n📋 Implementation Summary:")
        print("  ✓ Agent service client methods implemented")
        print("  ✓ Private authenticated routes in agent_routes.py")
        print("  ✓ Public marketplace routes in marketplace_routes.py")
        print("  ✓ Proto definitions updated with user_id authentication")
        print("  ✓ Visibility field handling resolved")
        return True
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)