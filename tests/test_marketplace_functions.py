import pytest
import grpc
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from app.services.marketplace_functions import AgentMarketplaceFunctionsService
from app.models.agent import AgentConfig, AgentConfigVersion, AgentMarketplaceListing
from app.grpc import agent_pb2
from app.utils.constants.constants import AgentStatusEnum, AgentVisibilityEnum, AgentCategoryEnum, AgentToneEnum, AgentOwnerTypeEnum, CategoryEnum


class TestAgentMarketplaceFunctionsService:
    """Test suite for AgentMarketplaceFunctionsService marketplace functions."""

    @pytest.fixture
    def service(self):
        """Create service instance for testing."""
        return AgentMarketplaceFunctionsService()

    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def sample_owner(self):
        """Create sample owner for testing."""
        owner = agent_pb2.Owner()
        owner.id = "user123"
        owner.email = "<EMAIL>"
        owner.full_name = "Test User"
        return owner

    @pytest.fixture
    def sample_marketplace_listing(self):
        """Create sample marketplace listing."""
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        return AgentMarketplaceListing(
            id="listing123",
            agent_config_id="agent123",
            agent_config_version_id="version123",
            version_number="1.0.0",
            listed_by_user_id="owner123",
            title="Test Agent",
            description="Test agent description",
            image_url="https://example.com/avatar.png",
            tags=["test", "automation"],
            use_count=5,
            execution_count=10,
            average_rating=4.5,
            visibility=AgentVisibilityEnum.PUBLIC,
            status=AgentStatusEnum.ACTIVE,
            created_at=now,
            updated_at=now
        )

    @pytest.fixture
    def sample_agent_config(self):
        """Create sample agent config."""
        return AgentConfig(
            id="agent123",
            name="Test Agent",
            description="Test agent description",
            avatar="https://example.com/avatar.png",
            owner_id="owner123",
            user_ids=["user1", "user2"],
            owner_type=AgentOwnerTypeEnum.USER,
            agent_template_id=None,
            template_owner_id=None,
            is_imported=False,
            agent_category=AgentCategoryEnum.ASSISTANT,
            system_message="You are a helpful assistant",
            workflow_ids=["workflow1"],
            mcp_server_ids=["mcp1"],
            visibility=AgentVisibilityEnum.PUBLIC,
            tags=["test", "automation"],
            status=AgentStatusEnum.ACTIVE,
            department="engineering",
            tone=AgentToneEnum.PROFESSIONAL,
            category=CategoryEnum.GENERAL,
            is_updated=False
        )

    @pytest.fixture
    def sample_agent_version(self):
        """Create sample agent config version."""
        return AgentConfigVersion(
            id="version123",
            agent_config_id="agent123",
            version_number="1.0.0",
            name="Test Agent",
            description="Test agent description",
            avatar="https://example.com/avatar.png",
            agent_category=AgentCategoryEnum.ASSISTANT,
            system_message="You are a helpful assistant",
            workflow_ids=["workflow1"],
            mcp_server_ids=["mcp1"],
            department="engineering",
            category=CategoryEnum.GENERAL,
            tags=["test", "automation"],
            status=AgentStatusEnum.ACTIVE
        )

    def test_createAgentFromTemplate_success(self, service, mock_db, sample_owner, sample_marketplace_listing, sample_agent_config, sample_agent_version):
        """Test successful agent creation from template."""
        # Arrange
        request = agent_pb2.CreateAgentFromTemplateRequest()
        request.template_id = "listing123"
        request.owner.CopyFrom(sample_owner)
        request.owner_type = agent_pb2.OwnerType.USER

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_marketplace_listing,  # First call for marketplace listing
            sample_agent_config,         # Second call for agent config
            sample_agent_version         # Third call for agent version
        ]
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.rollback = Mock()

        # Mock the service's get_db method
        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.createAgentFromTemplate(request, None)

            # Assert
            assert response.success is True
            assert "successfully created from template" in response.message
            mock_db.add.assert_called()
            mock_db.commit.assert_called_once()

    def test_createAgentFromTemplate_template_not_found(self, service, mock_db, sample_owner):
        """Test agent creation when template is not found."""
        # Arrange
        request = agent_pb2.CreateAgentFromTemplateRequest()
        request.template_id = "nonexistent"
        request.owner.CopyFrom(sample_owner)
        request.owner_type = agent_pb2.OwnerType.USER

        # Mock database to return None for marketplace listing
        mock_db.query.return_value.filter.return_value.first.return_value = None

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.createAgentFromTemplate(request, None)

            # Assert
            assert response.success is False
            assert "Template not found" in response.message

    def test_getMarketplaceAgentDetail_success(self, service, mock_db, sample_marketplace_listing, sample_agent_config):
        """Test successful marketplace agent detail retrieval."""
        # Arrange
        request = agent_pb2.GetMarketplaceAgentDetailRequest()
        request.id = "agent123"
        request.user_id = "user123"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_marketplace_listing,  # Marketplace listing query
            None                         # User's existing agent query (not found)
        ]

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.getMarketplaceAgentDetail(request, None)

            # Assert
            assert response.success is True
            assert response.agent.id == "agent123"
            assert response.agent.name == "Test Agent"
            assert response.agent.use_count == 5
            assert response.agent.average_rating == 4.5
            assert response.agent.is_added is False  # User hasn't used this agent

    def test_getMarketplaceAgentDetail_agent_not_found(self, service, mock_db):
        """Test marketplace agent detail when agent is not found."""
        # Arrange
        request = agent_pb2.GetMarketplaceAgentDetailRequest()
        request.id = "nonexistent"
        request.user_id = "user123"

        # Mock database to return None
        mock_db.query.return_value.filter.return_value.first.return_value = None

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.getMarketplaceAgentDetail(request, None)

            # Assert
            assert response.success is False
            assert "Agent not found in marketplace" in response.message

    def test_getMarketplaceAgentDetail_user_already_has_agent(self, service, mock_db, sample_marketplace_listing, sample_agent_config):
        """Test marketplace agent detail when user already has the agent."""
        # Arrange
        request = agent_pb2.GetMarketplaceAgentDetailRequest()
        request.id = "agent123"
        request.user_id = "user123"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_marketplace_listing,  # Marketplace listing query
            sample_agent_config          # User's existing agent query (found)
        ]

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.getMarketplaceAgentDetail(request, None)

            # Assert
            assert response.success is True
            assert response.agent.is_added is True  # User already has this agent

    def test_useAgent_success(self, service, mock_db, sample_marketplace_listing, sample_agent_config, sample_agent_version):
        """Test successful agent usage."""
        # Arrange
        request = agent_pb2.UseAgentRequest()
        request.agent_id = "agent123"
        request.user_id = "user123"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_marketplace_listing,  # Marketplace listing query
            None,                        # User's existing agent query (not found)
            sample_agent_config,         # Original agent config query
            sample_agent_version         # Agent version query
        ]
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.rollback = Mock()

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.useAgent(request, None)

            # Assert
            assert response.success is True
            assert "Agent successfully added to your collection" in response.message
            assert response.use_count == 6  # Original 5 + 1
            mock_db.add.assert_called()
            mock_db.commit.assert_called_once()

    def test_useAgent_already_used(self, service, mock_db, sample_marketplace_listing, sample_agent_config):
        """Test agent usage when user already has the agent."""
        # Arrange
        request = agent_pb2.UseAgentRequest()
        request.agent_id = "agent123"
        request.user_id = "user123"

        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_marketplace_listing,  # Marketplace listing query
            sample_agent_config          # User's existing agent query (found)
        ]

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.useAgent(request, None)

            # Assert
            assert response.success is False
            assert "You have already added this agent" in response.message

    def test_useAgent_agent_not_found(self, service, mock_db):
        """Test agent usage when agent is not found."""
        # Arrange
        request = agent_pb2.UseAgentRequest()
        request.agent_id = "nonexistent"
        request.user_id = "user123"

        # Mock database to return None
        mock_db.query.return_value.filter.return_value.first.return_value = None

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.useAgent(request, None)

            # Assert
            assert response.success is False
            assert "Agent not found in marketplace" in response.message

    def test_database_error_handling(self, service, mock_db, sample_owner):
        """Test database error handling in marketplace functions."""
        # Arrange
        request = agent_pb2.CreateAgentFromTemplateRequest()
        request.template_id = "listing123"
        request.owner.CopyFrom(sample_owner)
        request.owner_type = agent_pb2.OwnerType.USER

        # Mock database to raise an exception
        mock_db.query.side_effect = Exception("Database connection error")
        mock_db.rollback = Mock()

        with patch.object(service, 'get_db', return_value=mock_db):
            # Act
            response = service.createAgentFromTemplate(request, None)

            # Assert
            assert response.success is False
            assert "Failed to create agent from template" in response.message
            mock_db.rollback.assert_called_once()