import unittest
from unittest.mock import MagicMock, patch
import grpc
from datetime import datetime, timezone
from app.services.agent_functions import AgentFunctionsService
from app.models.agent import AgentConfig, AgentConfigVersion, AgentMarketplaceListing
from app.utils.constants.constants import Agent<PERSON>tatusEnum, AgentVisibilityEnum

# Mock request classes for testing
class MockRequest:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockFieldMask:
    def __init__(self, paths):
        self.paths = paths


class TestAgentVersioning(unittest.TestCase):
    def setUp(self):
        self.agent_functions = AgentFunctionsService()
        
        # Mock the database session
        self.db_mock = MagicMock()
        self.agent_functions.get_db = MagicMock(return_value=self.db_mock)
        
        # Mock gRPC context
        self.context_mock = MagicMock()
        
        # Test data
        self.agent_id = "test-agent-id"
        self.version_id = "test-version-id"
        self.user_id = "test-user-id"
        self.owner_mock = MagicMock()
        self.owner_mock.id = self.user_id
        
        # Mock agent with all required fields
        self.agent_mock = MagicMock(spec=AgentConfig)
        self.agent_mock.id = self.agent_id
        self.agent_mock.name = "Test Agent"
        self.agent_mock.description = "Test Description"
        self.agent_mock.avatar = "test-avatar.png"
        self.agent_mock.owner_id = self.user_id
        self.agent_mock.user_ids = []
        self.agent_mock.owner_type = "user"
        self.agent_mock.is_bench_employee = False
        self.agent_mock.is_imported = False
        self.agent_mock.agent_category = "general"
        self.agent_mock.system_message = "Test system message"
        self.agent_mock.model_provider = "openai"
        self.agent_mock.model_name = "gpt-4"
        self.agent_mock.model_api_key = "test-key"
        self.agent_mock.workflow_ids = []
        self.agent_mock.mcp_server_ids = []
        self.agent_mock.agent_topic_type = "general"
        self.agent_mock.visibility = AgentVisibilityEnum.PUBLIC
        self.agent_mock.tags = []
        self.agent_mock.status = AgentStatusEnum.ACTIVE
        self.agent_mock.created_at = datetime.now(timezone.utc)
        self.agent_mock.updated_at = datetime.now(timezone.utc)
        self.agent_mock.department = "engineering"
        self.agent_mock.organization_id = "test-org"
        self.agent_mock.tone = "professional"
        self.agent_mock.ruh_credentials = False
        self.agent_mock.files = []
        self.agent_mock.urls = []
        self.agent_mock.is_changes_marketplace = False
        self.agent_mock.is_a2a = False
        self.agent_mock.is_customizable = False
        self.agent_mock.capabilities_id = None
        self.agent_mock.example_prompts = []
        self.agent_mock.category = "general"
        self.agent_mock.variables = []
        self.agent_mock.is_updated = True
        self.agent_mock.current_version_id = None
        
        # Mock version with all required fields
        self.version_mock = MagicMock(spec=AgentConfigVersion)
        self.version_mock.id = self.version_id
        self.version_mock.agent_config_id = self.agent_id
        self.version_mock.version_number = "1.0.0"
        self.version_mock.name = "Test Agent"
        self.version_mock.description = "Test Description"
        self.version_mock.avatar = "test-avatar.png"
        self.version_mock.agent_category = "general"
        self.version_mock.system_message = "Test system message"
        self.version_mock.workflow_ids = []
        self.version_mock.mcp_server_ids = []
        self.version_mock.agent_topic_type = "general"
        self.version_mock.department = "engineering"
        self.version_mock.organization_id = "test-org"
        self.version_mock.ruh_credentials = False
        self.version_mock.tone = "professional"
        self.version_mock.is_bench_employee = False
        self.version_mock.is_changes_marketplace = False
        self.version_mock.is_a2a = False
        self.version_mock.is_customizable = False
        self.version_mock.capabilities_id = None
        self.version_mock.example_prompts = []
        self.version_mock.category = "general"
        self.version_mock.tags = []
        self.version_mock.status = AgentStatusEnum.ACTIVE
        self.version_mock.version_notes = "Initial version"
        self.version_mock.model_config_id = None
        self.version_mock.model_config = None
        self.version_mock.knowledge_base_id = None
        self.version_mock.knowledge_base = None
        self.version_mock.created_at = datetime.now(timezone.utc)
        self.version_mock.updated_at = datetime.now(timezone.utc)
        
        # Mock marketplace listing with all required fields
        self.listing_mock = MagicMock(spec=AgentMarketplaceListing)
        self.listing_mock.id = "test-listing-id"
        self.listing_mock.agent_config_id = self.agent_id
        self.listing_mock.agent_config_version_id = self.version_id
        self.listing_mock.title = "Test Agent Marketplace"
        self.listing_mock.description = "Test description"
        self.listing_mock.tags = []
        self.listing_mock.category = "general"
        self.listing_mock.is_featured = False
        self.listing_mock.download_count = 0
        self.listing_mock.rating = 0.0
        self.listing_mock.created_at = datetime.now(timezone.utc)
        self.listing_mock.updated_at = datetime.now(timezone.utc)

    def test_create_agent_version_success(self):
        """Test creating a new version of an agent successfully"""
        # Setup
        request = MockRequest(
            agent_id=self.agent_id,
            owner=self.owner_mock,
            version_number="1.1.0",
            version_notes="Added new features"
        )
        
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        self.db_mock.query.return_value.filter.return_value.order_by.return_value.first.return_value = self.version_mock
        
        # Execute
        response = self.agent_functions.createAgentVersion(request, self.context_mock)
        
        # Assert
        self.assertTrue(response['success'])
        self.assertIn("created successfully", response['message'])
        self.db_mock.add.assert_called()
        self.db_mock.commit.assert_called()

    def test_create_agent_version_not_found(self):
        """Test creating version for non-existent agent"""
        # Setup
        request = MockRequest(
            agent_id="non-existent-id",
            owner=self.owner_mock,
            version_number="1.1.0"
        )
        
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = None
        
        # Execute
        response = self.agent_functions.createAgentVersion(request, self.context_mock)
        
        # Assert
        self.assertFalse(response['success'])
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    def test_create_agent_version_permission_denied(self):
        """Test creating version without permission"""
        # Setup
        request = MockRequest(
            agent_id=self.agent_id,
            owner=self.owner_mock,
            version_number="1.1.0"
        )
        
        # Mock agent with different owner
        self.agent_mock.owner_id = "different-user-id"
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        
        # Execute
        response = self.agent_functions.createAgentVersion(request, self.context_mock)
        
        # Assert
        self.assertFalse(response['success'])
        self.context_mock.set_code.assert_called_with(grpc.StatusCode.PERMISSION_DENIED)

    def test_create_agent_version_no_updates(self):
        """Test creating version when agent has no updates"""
        # Setup
        request = MockRequest(
            agent_id=self.agent_id,
            owner=self.owner_mock,
            version_number="1.1.0"
        )
        
        # Mock agent with no updates
        self.agent_mock.is_updated = False
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        
        # Execute
        response = self.agent_functions.createAgentVersion(request, self.context_mock)
        
        # Assert
        self.assertFalse(response['success'])
        self.assertIn("No changes detected", response['message'])

    def test_publish_agent_to_marketplace_success(self):
        """Test publishing agent version to marketplace successfully"""
        # Setup
        request = MockRequest(
            agent_id=self.agent_id,
            version_number="1.0.0",
            owner=self.owner_mock,
            title="Test Agent Marketplace",
            description="Test description"
        )
        
        # Mock database queries for publish to marketplace
        agent_query_mock = MagicMock()
        agent_query_mock.filter.return_value.first.return_value = self.agent_mock
        
        version_query_mock = MagicMock()
        version_query_mock.filter.return_value.first.return_value = self.version_mock
        
        # Mock for checking existing listing (should return None)
        existing_listing_query_mock = MagicMock()
        existing_listing_query_mock.filter.return_value.first.return_value = None
        
        # Set up query to return different mocks for different calls
        self.db_mock.query.side_effect = [agent_query_mock, version_query_mock, existing_listing_query_mock]
        
        # Execute
        response = self.agent_functions.publishAgentToMarketplace(request, self.context_mock)
        
        # Assert
        self.assertTrue(response['success'])
        self.assertIn("published to marketplace", response['message'])
        self.db_mock.add.assert_called()
        self.db_mock.commit.assert_called()

    def test_list_agent_versions_success(self):
        """Test listing agent versions successfully"""
        # Setup
        request = MockRequest(
            agent_id=self.agent_id,
            user_id=self.user_id,
            page=1,
            page_size=10
        )
        
        # Mock database queries with proper chaining
        agent_query_mock = MagicMock()
        agent_query_mock.filter.return_value.first.return_value = self.agent_mock
        
        versions_query_mock = MagicMock()
        versions_query_mock.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [self.version_mock]
        versions_query_mock.filter.return_value.order_by.return_value.count.return_value = 1
        
        # Set up query to return different mocks for different calls
        self.db_mock.query.side_effect = [agent_query_mock, versions_query_mock, versions_query_mock]
        
        # Execute
        response = self.agent_functions.listAgentVersions(request, self.context_mock)
        
        # Assert
        self.assertTrue(response['success'])
        self.assertEqual(len(response['versions']), 1)
        self.assertEqual(response['total'], 1)

    def test_get_marketplace_listings_success(self):
        """Test getting marketplace listings successfully"""
        # Setup
        request = MockRequest(
            page=1,
            page_size=10
        )
        
        # Mock database queries for marketplace listings
        # Create a mock that handles the full query chain
        query_mock = MagicMock()
        filter_mock = MagicMock()
        offset_mock = MagicMock()
        limit_mock = MagicMock()
        
        # Set up the chain: query().filter().offset().limit().all()
        query_mock.filter.return_value = filter_mock
        filter_mock.offset.return_value = offset_mock
        offset_mock.limit.return_value = limit_mock
        limit_mock.all.return_value = [self.listing_mock]
        
        # Set up count for pagination
        filter_mock.count.return_value = 1
        
        # Mock the db.query to return our query mock
        self.db_mock.query.return_value = query_mock
        
        # Execute
        response = self.agent_functions.getMarketplaceListings(request, self.context_mock)
        
        # Assert
        self.assertTrue(response['success'])
        self.assertEqual(len(response['listings']), 1)
        self.assertEqual(response['total'], 1)

    def test_mark_agent_as_updated(self):
        """Test marking agent as updated when changes are made"""
        # Setup
        request = MockRequest(
            agent_id=self.agent_id,
            owner=self.owner_mock,
            name="Updated Name",
            update_mask=MockFieldMask(paths=["name"])
        )
        
        # Mock agent with is_updated = False initially
        self.agent_mock.is_updated = False
        self.agent_mock.name = "Original Name"
        self.db_mock.query.return_value.filter.return_value.first.return_value = self.agent_mock
        
        # Execute
        response = self.agent_functions.UpdateAgentCoreDetails(request, self.context_mock)
        
        # Assert
        self.assertTrue(response.success)
        # Verify that is_updated was set to True
        self.assertTrue(self.agent_mock.is_updated)


if __name__ == "__main__":
    unittest.main()