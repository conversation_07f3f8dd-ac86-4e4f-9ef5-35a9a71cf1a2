# User Service

A gRPC-based microservice for user authentication and management.

## Features

- User registration and authentication
- JWT-based authentication
- Google OAuth integration
- User profile management
- Password hashing and verification

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- PostgreSQL database

## Setup

1. Install dependencies:

```bash
poetry install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Application Settings
APP_NAME=user-service
DEBUG=true
PORT=50052

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=user_service
DB_PASSWORD=userpass
DB_NAME=user_db

# JWT Settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth Settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Running the Service

Use the provided script:

```bash
chmod +x run_local.sh
./run_local.sh
```

Or run manually:

```bash
# Install dependencies
poetry install

# Initialize database
poetry run python -m app.db.init_db

# Start the service
poetry run python -m app.main
```

The service will start on port 50052.

## API Documentation

### gRPC Methods

- `register`: Register a new user
- `login`: Authenticate user and get tokens
- `googleOAuthLogin`: Login with Google OAuth
- `refreshToken`: Get new access token using refresh token
- `getUser`: Get user profile
- `updateUser`: Update user information
- `deleteUser`: Delete user account
- `listUsers`: Get paginated list of users
- `searchUsers`: Search users by email or name

## Development

### Project Structure

```
user-service/
├── app/
│   ├── api/
│   ├── core/          # Core functionality (config, security)
│   ├── db/            # Database models and session
│   ├── grpc/          # Generated gRPC code
│   ├── models/        # SQLAlchemy models
│   ├── schemas/       # Pydantic models
│   └── services/      # Business logic
├── proto-definitions/ # Proto files
├── tests/            # Test files
├── .env.example      # Example environment variables
├── poetry.lock       # Lock file for dependencies
├── pyproject.toml    # Project configuration
└── README.md         # This file
```

### Testing

Run tests with:

```bash
poetry run pytest
```

### Generating gRPC Code

After modifying proto files:

```bash
poetry run python -m app.scripts.generate_grpc
```
